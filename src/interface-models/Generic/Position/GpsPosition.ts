import {
  convertHeadingToCardinalDirection,
  isNumeric
} from '@/helpers/DistanceHelpers/DistanceHelpers';
import { LngLat } from 'mapbox-gl';

export interface IGpsPosition {
  _id?: string;
  company: string;
  division: string;
  uploadId: string;
  timestamp: string;
  latitude: string;
  longitude: string;
  accuracy: string;
  heading: string;
  speed: string;
  speedAccuracy: string;
  jobId: string;
  pudId: string;
  isMergeJob?: boolean;
  driverId: string;
  deviceId: string;
  fleetAssetId: string;
  clientId: string;
  epochTime: number;
  isInactive: boolean;
  inactiveDuration: number;
  description?: string;
}

export class GpsPosition implements IGpsPosition {
  public _id?: string;
  public isMergeJob?: boolean;
  public description?: string;
  constructor(
    public company: string = '',
    public division: string = '',
    public uploadId: string = '',
    public timestamp: string = '',
    public latitude: string = '',
    public longitude: string = '',
    public accuracy: string = '',
    public heading: string = '',
    public speed: string = '',
    public speedAccuracy: string = '',
    public jobId: string = '',
    public pudId: string = '',
    public driverId: string = '',
    public deviceId: string = '',
    public fleetAssetId: string = '',
    public clientId: string = '',
    public epochTime: number = 0,
    public isInactive: boolean = false,
    public inactiveDuration: number = 0
  ) {}

  get asLngLat() {
    return new LngLat(parseFloat(this.longitude), parseFloat(this.latitude));
  }
  get headingAsCardinalDirection() {
    if (isNumeric(this.heading)) {
      return convertHeadingToCardinalDirection(parseFloat(this.heading));
    }
    return '';
  }

  get speedAsKph(): number {
    if (isNumeric(this.speed)) {
      const asFloat = parseFloat(this.speed);
      return asFloat > 0 ? asFloat * 3.6 : 0;
    } else {
      return 0;
    }
  }
}

/**
 * Interface for GPS device group containing grouped GPS position data
 */
export interface GpsDeviceGroup {
  deviceId: string;
  driverId: string;
  fleetAssetId: string;
  dataCount: number;
  positions: GpsPosition[];
  displayName: string;
}

/**
 * Interface for device selection option in dropdown
 */
export interface DeviceSelectionOption {
  value: string;
  text: string;
  dataCount: number;
}

export default GpsPosition;
