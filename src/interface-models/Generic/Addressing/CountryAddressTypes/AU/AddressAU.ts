import SuburbAU from './SuburbAU';

export interface AddressAUInterface {
  addressId: string;
  formattedAddress: string;
  addressLine1: string | undefined;
  addressLine2: string | undefined | null;
  addressLine3: string | undefined | null;
  addressLine4: string | undefined;
  suburb: string | undefined;
  postcode: string | undefined;
  city: string | undefined | null;
  state: string | undefined;
  country: string | undefined;
  geoLocation: number[]; // ORS response is in format [LONGITUDE, LATITUDE]
}

export class AddressAU implements AddressAUInterface {
  constructor(
    public addressId: string = '',
    public formattedAddress: string = '',
    public addressLine1: string = '',
    public addressLine2: string = '',
    public addressLine3: string = '',
    public addressLine4: string = '',
    public suburb: string = '',
    public postcode: string = '',
    public city: string = '',
    public state: string = '',
    public country: string = '',
    public geoLocation: number[] = [0, 0], // ORS response is in format [LONGITUDE, LATITUDE]
  ) {}

  public fromSuburb(suburb: SuburbAU) {
    this.addressId = suburb.subId || suburb.id || `sub${suburb.postcode}`;
    this.suburb = suburb.name;
    this.state = suburb.state;
    this.postcode = suburb.postcode;
    this.geoLocation = suburb.geoLocation;
    this.formattedAddress = suburb.formattedAddress;
  }

  public toSuburb(): SuburbAU {
    return new SuburbAU(
      this.addressId,
      `sub${this.postcode}`,
      this.formattedAddress || '',
      this.suburb,
      this.postcode || '',
      this.state,
      this.geoLocation,
    );
  }
}

export default AddressAU;
