export interface ISuburbAU {
  id: string;
  subId: string;
  formattedAddress: string;
  name: string;
  postcode: string;
  state: string;
  geoLocation: number[];
  distance?: number;
}

export class SuburbAU implements ISuburbAU {
  distance?: number | undefined;
  constructor(
    public id: string = '',
    public subId: string = '',
    public formattedAddress: string = '',
    public name: string = '',
    public postcode: string = '',
    public state: string = '',
    public geoLocation: number[] = [],
  ) {}
}

export default SuburbAU;
