/**
 * Represents a service type and its associated variation for distance rate
 * pricing report.
 *
 * Holds information about the service type identifier and an optional client
 * adjustment percentage.
 */
export interface ServiceTypeAndVariation {
  /**
   * The unique identifier for the service type.
   */
  serviceTypeId: number;
  /**
   * A custom percentage adjustment specified by the user on the frontend when generating the Distance Rate Pricing report.
   *
   * This value may be `null` or `undefined` if no adjustment is provided, in which case the appropriate rate variation percentage will be requested from the database before report generation.
   */
  clientAdjustmentPercentage?: number | null;
}
