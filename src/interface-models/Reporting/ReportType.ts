import { UserRoleType } from '../Roles/UserRoles';

export enum ReportType {
  MARGIN = 'MARGIN',
  DAILY_JOBS = 'DAILY_JOBS',
  REMITTANCE_SUMMARY = 'REMITTANCE_SUMMARY',
  <PERSON><PERSON><PERSON>_MANGER_KPI = 'BRANCH_MANAGER_KPI',
  OPERATIONAL_KPI = 'OPERATIONAL_KPI',
  INVOICE_FINANCES_SUMMARY = 'INVOICE_FINANCES_SUMMARY',
  SUBCONTRACTOR_BANK_DETAILS = 'SUBCONTRACTOR_BANK_DETAILS',
  SUBCONTRACTOR_COMPLIANCE = 'SUBCONTRACTOR_COMPLIANCE',
  FLEET_ASSET_COMPLIANCE = 'FLEET_ASSET_COMPLIANCE',
  DRIVER_COMPLIANCE = 'DRIVER_COMPLIANCE',
  DRIVER_PAY = 'DRIVER_PAY',
  STANDBY_INVOICE = 'STANDBY_INVOICE',
  COST_PER_TONNE = 'COST_PER_TONNE',
  YARD_DELAY = 'YARD_DELAY',
  CLIENT_KPI_DAILY = 'CLIENT_KPI_DAILY',
  PROOF_OF_DELIVERY = 'PROOF_OF_DELIVERY',
  INVOICE_SUMMARY = 'INVOICE_SUMMARY',
  SALES_BY_CLIENT = 'SALES_BY_CLIENT',
  RANK_SALES_BY_REVENUE = 'RANK_SALES_BY_REVENUE',
  CLIENT_KPI_TIME_RATE = 'CLIENT_KPI_TIME_RATE',
  OPERATOR_PERFORMANCE = 'OPERATOR_PERFORMANCE',
  DELIVERY_PERFORMANCE = 'DELIVERY_PERFORMANCE',
  SERVICE_TOTALS = 'SERVICE_TOTALS',
  CLIENT_DETAILS_SUMMARY = ' CLIENT_DETAILS_SUMMARY',
  CLIENT_CONTACTS = 'CLIENT_CONTACTS',
  SERVICE_RATES = 'SERVICE_RATES',
  SALES_BY_SALES_PERSON = 'SALES_BY_SALES_PERSON',
  DRIVER_CONTACT_DETAILS = 'DRIVER_CONTACT_DETAILS',
  SERVICE_FAILURES = 'SERVICE_FAILURES',
  RANK_SALES_BY_REVENUE_BY_SALES_PERSON = 'RANK_SALES_BY_REVENUE_BY_SALES_PERSON',
  JOB_DETAILS = 'JOB_DETAILS',
  SUBCONTRACTOR_DETAILS = 'SUBCONTRACTOR_DETAILS',
  DISTANCE_RATE_PRICING = 'DISTANCE_RATE_PRICING',
}

export enum ReportCategory {
  CLIENT_REPORTING = 'CLIENT_REPORTING',
  OPERATIONS_REPORTING = 'OPERATIONS_REPORTING',
  HEAD_OFFICE_REPORTING = 'HEAD_OFFICE_REPORTING',
}

export interface ReportColumnDescription {
  name: string;
  description: string;
  calculation: string;
}

export interface ReportDetails {
  category: ReportCategory;
  name: string;
  reportType: ReportType;
  endPoint: string;
  description: string;
  audience: string;
  query: string[];
  fileName: string[];
  permissions: UserRoleType[];
  columns: ReportColumnDescription[];
}

export const reports: ReportDetails[] = [
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'Email Daily Jobs Report',
    reportType: ReportType.DAILY_JOBS,
    endPoint: 'generateAndEmailDailyJobReports',
    description:
      'This report function emails a comprehensive summary of transport services provided for the specified date range to each client that has been configured to receive Daily Jobs Reports in their client details configuration. This report is designed to provide a detailed breakdown of each job performed, providing valuable insights into the efficiency and performance of the transport operations. Additionally, you can exclude specific clients from being emailed the report in this process by using the "Exclude Clients" input parameter, ensuring that the information provided is highly relevant to your specific needs.',
    audience: 'The report is intended to be provided to clients.',
    // prettier-ignore
    query: [
      'Client\'s that have "Daily Jobs Report" checked in their email settings.',
      'Client\'s that have at least one shared Dispatcher email address or a dispatcher that has recieves emails checked.',
      'Client\'s that have jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised'
    ],
    fileName: ['Daily Jobs Report'],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Date',
        description: 'The date on which each job was booked to be completed.',
        calculation: 'Equal to the defined arrival time of the first leg.',
      },
      {
        name: 'Vehicle',
        description:
          'Identifies the specific service and rate type assigned to each job.',
        calculation: 'Short name of the applied service and rate type.',
      },
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: '',
      },
      {
        name: 'Destination',
        description:
          'Specifies the destination of each leg on the job. The first line item for the job will include the first and last suburb.',
        calculation: 'The address suburb name.',
      },
      {
        name: 'Start',
        description:
          'Indicates the starting time of each job determined by the either the billable time on the job for hourly rates or the time the job was started by the driver.',
        calculation: '',
      },
      {
        name: 'Finish',
        description:
          'Indicates the ending time of each job determined by the either the billable time on the job for hourly rates or the time the job was completed by the driver.',
        calculation: '',
      },
      {
        name: 'References',
        description: 'References related to the leg or job.',
        calculation: '',
      },
      {
        name: 'Driver',
        description: 'The Fleet Asset ID on the job.',
        calculation: '',
      },
      {
        name: 'hrs',
        description: 'Records the number of hours worked on each job.',
        calculation: 'Billed duration defined on the job at pricing.',
      },
      {
        name: 'Break',
        description: 'Accounts for breaks taken during the job.',
        calculation: 'Break duration defined on the job at pricing',
      },
      {
        name: 'Rate',
        description: 'Specifies the rate or pricing structure for each job.',
        calculation: 'The rate applied to billed duration, fuel levy or unit.',
      },
      {
        name: 'Amount',
        description:
          'Total cost or revenue excluding GST generated for each job/leg.',
        calculation: 'Quantity * Rate',
      },
      {
        name: 'Fuel',
        description:
          'Fuel levy cost or revenue excluding GST generate for each job/leg.',
        calculation: 'freight charge * fuel levy',
      },
      {
        name: 'GST',
        description:
          'Records the Goods and Services Tax (GST) associated with each job/leg.',
        calculation: 'Amount * 0.10',
      },
      {
        name: 'Total',
        description: 'Calculates the overall total for each job/leg.',
        calculation: 'Amount + GST',
      },
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'Daily Jobs Report',
    reportType: ReportType.DAILY_JOBS,
    endPoint: 'generateReport/getClientDailyJobsReport',
    description:
      'This report generates a comprehensive summary of transport services provided for the specified date range for the specified client. This report is designed to provide a detailed breakdown of each job performed, providing valuable insights into the efficiency and performance of the transport operations.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Jobs associated with the selected client.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    fileName: [
      'Daily Jobs Report',
      'company',
      'division',
      'clientName',
      'clientId',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Date',
        description: 'The date on which each job was booked to be completed.',
        calculation: 'Equal to the defined arrival time of the first leg.',
      },
      {
        name: 'Vehicle',
        description:
          'Identifies the specific service and rate type assigned to each job.',
        calculation: 'Short name of the applied service and rate type.',
      },
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: '',
      },
      {
        name: 'Destination',
        description:
          'Specifies the destination of each leg on the job. The first line item for the job will include the first and last suburb.',
        calculation: 'The address suburb name.',
      },
      {
        name: 'Start',
        description:
          'Indicates the starting time of each job determined by the either the billable time on the job for hourly rates or the time the job was started by the driver.',
        calculation: '',
      },
      {
        name: 'Finish',
        description:
          'Indicates the ending time of each job determined by the either the billable time on the job for hourly rates or the time the job was completed by the driver.',
        calculation: '',
      },
      {
        name: 'References',
        description: 'References related to the leg or job.',
        calculation: '',
      },
      {
        name: 'Driver',
        description:
          'The identifier for the Fleet Asset ID assigned to the job.',
        calculation: '',
      },
      {
        name: 'hrs',
        description: 'Records the number of hours worked on each job.',
        calculation: 'Billed duration defined on the job at pricing.',
      },
      {
        name: 'Break',
        description: 'Accounts for breaks taken during the job.',
        calculation: 'Break duration defined on the job at pricing',
      },
      {
        name: 'Rate',
        description: 'Specifies the rate or pricing structure for each job.',
        calculation: 'The rate applied to billed duration, fuel levy or unit.',
      },
      {
        name: 'Amount',
        description:
          'Total cost or revenue excluding GST generated for each job/leg.',
        calculation: 'Quantity * Rate',
      },
      {
        name: 'Fuel',
        description:
          'Fuel levy cost or revenue excluding GST generate for each job/leg.',
        calculation: 'freight charge * fuel levy',
      },
      {
        name: 'GST',
        description:
          'Records the Goods and Services Tax (GST) associated with each job/leg.',
        calculation: 'Amount * 0.10',
      },
      {
        name: 'Total',
        description: 'Calculates the overall total for each job/leg.',
        calculation: 'Amount + GST',
      },
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'Standby Invoice',
    reportType: ReportType.STANDBY_INVOICE,
    endPoint: 'generateReport/getStandbyInvoiceReport',
    // prettier-ignore
    description:
      'Our Standby Invoice Report is designed to provide clients that have a standby rate configured with a detailed report of jobs that had standby rates applied and should supplement the invoice . This report can be generated for a specified client, allowing input for a start and end date. It primarily focuses on jobs with standby time rates that have been reviewed. It\'s important to note that the report\'s structure may not adhere to a uniform pattern. Instead, it adapts dynamically based on the completion of the individual jobs standby charges. Rows within the report are categorized into sections to differentiate legs involving standby time from those without standby time. Consequently, a single job may span across multiple rows, highlighting the unique nature of each job\'s execution.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Jobs associated with the selected client.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    fileName: [
      ReportType.STANDBY_INVOICE.toString(),
      'report',
      'company',
      'division',
      'clientName',
      'clientId',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Date',
        description: 'The date on which each job was booked to be completed.',
        calculation: 'Equal to the defined arrival time of the first leg.'
      },
      {
        name: 'Fleet',
        description:
          'The identifier for the Fleet Asset ID assigned to the job.',
        calculation: ''
      },
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: ''
      },
      {
        name: 'Load',
        description:
          'The reference linked to the standby stop. All following rows (stops) that don\'t have a standby rate will use the reference from the initial standby stop.',
        calculation: ''
      },
      {
        name: 'Tonnes',
        description:
          'The total weight specified for pickup/dropoff legs. Standby line items do not display a weight, but the weight will be recorded on non-standby line items.',
        calculation: ''
      },
      {
        name: 'Start',
        description:
          'The start time for the standby/non-standby section of a job.',
        calculation: ''
      },
      {
        name: 'Finish',
        description:
          'The finish time for the standby/non-standby section of a job.',
        calculation: ''
      },
      {
        name: 'Time',
        description:
          'The total duration for the standby/non-standby section of a job.',
        calculation: 'Finish - Start'
      },
      {
        name: 'Break Min',
        description:
          'The total break duration for the standby/non-standby section of a job.',
        calculation: ''
      },
      {
        name: 'Rate Type',
        // prettier-ignore
        description: 'The service type. Prefixed with \'SB\' to signify the application of standby charges.',
        calculation: ''
      },
      {
        name: 'Rate',
        description: 'The applied service rate.',
        calculation: ''
      },
      {
        name: 'Cost',
        description: 'The cost of the service rate',
        calculation: '(duration of section of job / multiplier) * rate'
      },
      {
        name: 'Toll',
        description: 'The toll charge associated with the job.',
        calculation: 'Displayed on the last non-standby section of a job.'
      },
      {
        name: 'Total Cost',
        description: 'The total cost associated with the particular line item.',
        calculation: 'Cost + Toll'
      }
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'Yard Delay',
    reportType: ReportType.YARD_DELAY,
    endPoint: 'generateReport/getYardDelayReport',
    // prettier-ignore
    description: 'The Yard Delay Report provides the specified client with a report on the amount of time spent on-site that exceeds the expected duration. This report is generated for a specific client and allows input for a start and end date, excess delay duration, and whether to report for dispatch locations only. It\'s important to note that jobs can span across multiple lines, where each line represents a leg of the job that exceeded the delay duration. The report highlights jobs that exceed the expected duration of a leg\'s loading time as determined by the input field.',
    audience:
      'The report is intended to be provided to clients, but can also be helpful for internal reporting.',
    query: [
      'Jobs associated with the selected client.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    fileName: [
      ReportType.YARD_DELAY.toString(),
      'report',
      'company',
      'division',
      'clientName',
      'clientId',
      'runDate',
    ],
    columns: [
      {
        name: 'Date',
        description: 'The date on which each job was booked to be completed.',
        calculation: 'Equal to the defined arrival time of the first leg.',
      },
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: '',
      },
      {
        name: 'Fleet',
        description:
          'The identifier for the Fleet Asset ID assigned to the job.',
        calculation: '',
      },
      {
        name: 'Driver',
        description: 'The driver who has been assigned to the job.',
        calculation: '',
      },
      {
        name: 'Leg Type',
        description:
          'Specifies whether the leg was a pickup (P) or a drop-off (D).',
        calculation: '',
      },
      {
        name: 'Load #',
        description: 'The reference associated with the leg.',
        calculation: '',
      },
      {
        name: 'Location',
        description: 'Specifies the destination of each leg on the job.',
        calculation: 'The address suburb name.',
      },
      {
        name: 'Onsite Time.',
        description:
          'The time at which the arrival event occurred for the leg.',
        calculation: '24hr time format',
      },
      {
        name: 'Time Depart.',
        description:
          'The time at which the departure event occurred for the leg.',
        calculation: '24hr time format',
      },
      {
        name: 'Total Time',
        description:
          'The total duration of the loading or unloading time for all the legs, measured in minutes.',
        calculation: 'Time Depart - onsite Time',
      },
      {
        name: 'Additional Time',
        description:
          'The total duration of time that exceeded the expected or planned duration.',
        calculation: 'Total Time - Delay Duration',
      },
      {
        name: 'Run #',
        description: 'The specific leg number within the job.',
        calculation: '',
      },
      {
        name: 'Delay Code',
        description: 'Intentially left blank for manual user input.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'KPI - Cost Per Tonne',
    reportType: ReportType.COST_PER_TONNE,
    endPoint: 'generateReport/kpiCostPerTonne',
    description:
      'The Cost Per Tonne report is a key performance indicator (KPI) tool that provides valuable insight to the specified client as to the transport costs associated to each tonne of goods delivered, enabling you to make informed decisions to enhance efficiency and profitability in their operations. You have the flexibility to choose between tonnage calculated on pickups or deliveries or pickup manifest items when calculating the weight. The option also exists to exclude return trips and actual distance calculations, tailoring the analysis to your specific needs.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Jobs associated with the selected client.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    fileName: [
      ReportType.COST_PER_TONNE.toString(),
      'report',
      'company',
      'division',
      'clientName',
      'clientId',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Job',
        description: 'A unique identifier for each job.',
        calculation: '',
      },
      {
        name: 'Date',
        description: 'The date on which each job was booked for.',
        calculation:
          'Equal to the defined arrival time of the first leg of the job.',
      },
      {
        name: 'Driver',
        description: 'The driver assigned to the job.',
        calculation: '',
      },
      {
        name: 'Perm/Adhoc',
        description: 'Indicates whether the client is permanent or adhoc.',
        calculation: 'Client Type defined in the clients key details.',
      },
      {
        name: 'Vehicle Type',
        description: 'The type of service assigned to the job.',
        calculation: '',
      },
      {
        name: 'Rate Type',
        description: 'The type of rate assigned to the job.',
        calculation: '',
      },
      {
        name: '# of Deliveries',
        description:
          'The total number of deliveries on the job, which may vary based on whether the "Exclude Return To First Leg" parameter is checked.',
        calculation: 'A count of all drop-off legs.',
      },
      {
        name: 'Weight',
        description: 'The total weight on the job.',
        calculation: 'Determined based on the Pickup/Dropoff report parameter.',
      },
      {
        name: 'Planned Km Driven',
        description:
          'The distance traveled as defined by the route taken for the job.',
        calculation: '',
      },
      // prettier-ignore
      {
        name: 'Actual Km Driven',
        description:
          'The distance traveled as determined by the driver\'s GPS data.',
        calculation: 'Haversine formula'
      },
      {
        name: 'Freight Subtotal',
        description: 'The total freight charges, excluding GST.',
        calculation: '',
      },
      {
        name: 'Toll',
        description: 'The total toll charges excluding GST.',
        calculation: 'Toll charge excluding GST',
      },
      {
        name: 'Fuel Levy',
        description: 'The total fuel charges excluding GST.',
        calculation: 'Fuel charge excluding GST',
      },
      {
        name: 'Total Charge Inc Fuel',
        description: 'The total Freight, Toll and fuel charge excluding Gst.',
        calculation: 'Freight Subtotal + Toll + Fuel Levy',
      },
      {
        name: 'Hours',
        description: 'The overall duration of the job, measured in hours.',
        calculation:
          'The times of events on the job or the edited billable time.',
      },
      {
        name: 'Total On Site Time (mins)',
        description:
          'The total load duration for all legs on the job in minutes.',
        calculation: '',
      },
      {
        name: 'Average On Site Time / Stop (min)',
        description:
          'The combined loading duration for all legs of the job, measured in minutes.',
        calculation: 'Total On Site Time (mins) / # of Deliveries',
      },
      {
        name: 'Average Job Time / Stop (min)',
        description: 'The average duration per leg, measured in minutes.',
        calculation: 'Hours / delivery legs',
      },
      {
        name: 'Cost / Tonne',
        description:
          'The total charge amount per tonne. Weight is calculated based on the pickup/dropoff weight parameter.',
        calculation: 'Total Charge Inc Fuel / weight.',
      },
      {
        name: 'Cost / Stop',
        description: 'The total charge amount per delivery location.',
        calculation: 'Total Charge Inc Fuel / # of Deliveries.',
      },
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'KPI - Daily',
    reportType: ReportType.CLIENT_KPI_DAILY,
    endPoint: 'generateReport/getClientKpiDailyReport',
    // prettier-ignore
    description: 'The Client KPI Daily Report is a key performance indicator (KPI) tool that provides valuable insights to the specified client as to their transport performance. With the ability to select your client, date range, and optional checkboxes for actual distance calculations and excluding return trips, this report empowers you to make informed decisions for efficient and cost-effective daily operations.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Jobs associated with the selected client.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    fileName: [
      ReportType.CLIENT_KPI_DAILY.toString(),
      'report',
      'company',
      'division',
      'clientName',
      'clientId',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Date',
        description: 'The date against which calculations will be performed.',
        calculation: ''
      },
      {
        name: '# of Jobs',
        description: 'The total count of jobs.',
        calculation: ''
      },
      {
        name: 'Cost Excl Fuel/Tolls/Gst',
        description:
          'The total cost of the jobs, excluding fuel, tolls, and GST.',
        calculation: 'Total cost - fuel - tolls'
      },
      {
        name: 'Total Tonnes',
        description: 'The total weight of all pickups, measured in tonnes.',
        calculation: 'A sum of all pickups across all jobs on the date.'
      },
      {
        name: '# of Dropoffs',
        description: 'The total count of dropoffs.',
        calculation: ''
      },
      {
        name: '# of Pickups',
        description: 'The total count of pickups.',
        calculation: ''
      },
      {
        name: 'Drops / Pickups',
        description:
          'The number of pickups for each dropoff that occurred on the specified date.',
        calculation: '# of Dropoffs / # of Pickups'
      },
      {
        name: '(Cost Excl Fuel/Tolls/Gst) / Tonnes',
        description: 'The average cost per tonne.',
        calculation: 'Cost Excl Fuel/Tolls/Gst / Total Tonnes.'
      },
      {
        name: 'Cost / Drops',
        description: 'The average cost per drop.',
        calculation: ''
      },
      {
        name: 'Tonne / Drops',
        description: 'The average tonnes delivered per dropoff.',
        calculation: 'Total Tonnes / # of Dropoffs.'
      },
      {
        name: 'Tonne / Pickups',
        description: 'The average tonnes delivered per pickup.',
        calculation: 'Total Tonnes / # of Pickups.'
      },
      {
        name: 'Pickup Load Duration (mins)',
        description:
          'The sum of loading durations for all pickup legs across all jobs on the specified date.',
        calculation: 'Sum of (Departure event time - Arrival event time)'
      },
      {
        name: 'Pickup Load / Pickups',
        description:
          'The average loading time at a pickup leg across all jobs on the specified date.',
        calculation: 'Pickup Load Duration (mins) / # of Pickups'
      },
      {
        name: 'Total Duration (mins)',
        description:
          'The sum of billable durations for all jobs on the specified date.',
        calculation:
          'The times of events on the job or the edited billable time.'
      },
      {
        name: 'Total Duration (mins) / Days',
        description:
          'This column corresponds to the Total Duration (mins) column, with each line item representing one day. The totals line item at the bottom showcases the average daily duration across all days in the report.',
        calculation: 'Total Duration (mins) / Number of Days'
      },
      {
        name: 'Total Duration / Tonnes',
        description: 'The average duration per tonne delivered.',
        calculation: 'Total Duration (mins) / Total Tonnes'
      },
      {
        name: 'Total Drop Duration (mins)',
        description:
          'The sum of delivery loading durations across all jobs on the specified date.',
        calculation:
          'The times of events on the job or the edited billable time.'
      },
      {
        name: 'Total Drop Duration / Drops',
        description: 'The average duration at delivery locations.',
        calculation: 'Total Drop Duration (mins) / # of Dropoffs'
      },
      {
        name: 'Tolls Cost',
        description:
          'The sum of all toll charges, excluding GST, for all jobs on the specified date.',
        calculation: ''
      },
      {
        name: 'Tolls Cost / Tonnes',
        description: 'The average charge for tolls per tonne delivered.',
        calculation: 'Tolls Cost / Total Tonnes'
      },
      {
        name: 'Cost Excl fuel + Toll / Tonnes',
        description:
          'The average charge made to the client per tonne, excluding fuel and GST.',
        calculation:
          'The sum of (total charge - fuel) excluding gst / Total Tonnes'
      },
      {
        name: 'Planned Km Driven',
        description:
          'The distance traveled as defined by the route taken for the job.',
        calculation: ''
      },
      {
        name: 'Actual Km Driven',
        description: 'The distance traveled as determined by the driver\'s GPS data.',
        calculation: 'Haversine formula'
      }
    ],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'KPI - Time Rate',
    reportType: ReportType.CLIENT_KPI_TIME_RATE,
    endPoint: 'generateReport/getClientKpiReport',
    // prettier-ignore
    description: 'The Client KPI Daily Report is a key performance indicator (KPI) tool that provides valuable insights to the specified client as to their transport performance specifically for jobs with time-based rates. This report groups jobs by fleet asset + service type, with the ability to select multiple clients, date range, and optional checkboxes for actual distance calculations and excluding return trips, this report provides clients with detailed information on the transport costs of their operations.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Jobs that associated with the selected clients.',
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised',
    ],
    fileName: [
      ReportType.CLIENT_KPI_TIME_RATE.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Division',
        description: 'The division within which the report is being produced.',
        calculation: '',
      },
      {
        name: 'Client',
        description: 'The client specified separated into sections.',
        calculation: '',
      },
      {
        name: 'Driver',
        description:
          'The identifier for the Fleet Asset ID assigned to the jobs, each row being uniquely identified by Driver + Vehicle Type',
        calculation: '',
      },
      {
        name: 'Vehicle Type',
        description:
          'The service type assigned with the jobs, each row being uniquely identified by Driver + Vehicle Type.',
        calculation: '',
      },
      {
        name: 'Perm / Adhoc',
        description: 'Whether the client is  permanent or adhoc.',
        calculation: 'Client Type defined in the clients key details.',
      },
      {
        name: 'Hourly Rate',
        description: 'The hourly time rate applied to the service type.',
        calculation: '',
      },
      {
        name: '# Loads',
        description: 'A sum of all pickups for the asset and service type.',
        calculation: '',
      },
      {
        name: 'Average Drops Per Load',
        description: 'The average number of delivery legs for every pickup leg',
        calculation: '# Drops / # Loads',
      },
      {
        name: 'Average Hours Per Load',
        description: 'The average time spent for every pickup.',
        calculation: 'Total Hours Worked / # Loads',
      },
      {
        name: '# Drops',
        description: 'A sum of all deliveries for the asset and service type.',
        calculation: '',
      },
      {
        name: 'Average Drops Per Hour',
        description:
          'The average number of deliveries completed per hour for the fleet asset and service type.',
        calculation: '# Drops / Total Hours Worked',
      },
      {
        name: 'Total Hours Worked',
        description:
          'The total hours worked for the fleet asset and service type.',
        calculation:
          'A sum of (finish event time - started event time) or edited billable time.',
      },
      {
        name: 'Total Days Worked',
        description:
          'The number of days worked by the fleet asset and service type.',
        calculation:
          'A unique count of dates on all jobs for the fleet asset and service type.',
      },
      {
        name: 'Average Hours Per Day',
        description: 'The average hours worked per day.',
        calculation: 'Total Hours Worked / Total Days Worked',
      },
      {
        name: 'Total Time At Depot',
        description: 'A sum of the total time spent at all pickup legs.',
        calculation:
          'A sum of (departure - started) event times for pickup legs',
      },
      {
        name: 'Actual Time At customer',
        description: 'A sum of the total time spent at all delivery legs.',
        calculation:
          'A sum of (departure - started) event times for delivery legs.',
      },
      {
        name: 'Average Load Time (mins)',
        description: 'The average time spent at pickup legs.',
        calculation: 'Total Time At Depot / # Loads',
      },
      {
        name: 'Average Unload Time (mins)',
        description: 'The average time spent at delivery legs.',
        calculation: 'Actual Time At customer / # Drops',
      },
      {
        name: 'Average Transit Time (mins)',
        description: 'The average time in transit for every job executed.',
        calculation: 'A sum of all jobs actual drive duration / Number of Jobs',
      },
      {
        name: 'Total Cost',
        description:
          'The total cost minus fuel excluding GST for the jobs executed by the fleet asset and service type.',
        calculation: 'A sum of (total cost - fuel cost) excluding GST',
      },
      {
        name: 'Average Cost Per Drop',
        description: 'The average cost associated with each delivery leg.',
        calculation: 'Total Cost / # Drops',
      },
      {
        name: '$ Value of Depot ',
        description:
          'The hourly rate applied to the total duration at pickup legs.',
        calculation: 'Hourly Rate * Total Time At Depot',
      },
      {
        name: 'Planned Km Driven',
        description:
          'The distance traveled as defined by the route taken for the job.',
        calculation: '',
      },
      // prettier-ignore
      {
        name: 'Actual Km Driven',
        description:
          'The distance traveled as determined by the driver\'s GPS data.',
        calculation: 'Haversine formula'
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Margin Report',
    reportType: ReportType.MARGIN,
    endPoint: 'generateReport/getMarginReport',
    // prettier-ignore
    description: 'The Margin Report is a powerful tool that offers a comprehensive view of revenue and expenses associated with each jobs, including charges related to equipment hire. This report provides in-depth insights into the profitability of your client relationships, ensuring you have a complete understanding of your financial performance. By detailing revenue, expenses, and equipment hire charges for each job, this report empowers you to make data-driven decisions and optimize your client management strategies.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised.',
    ],
    fileName: [
      ReportType.MARGIN.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: ''
      },
      {
        name: 'Date',
        description: 'The date on which each job was booked for.',
        calculation:
          'Equal to the defined arrival time of the first leg of the job.'
      },
      {
        name: 'Serv (Client)',
        description: 'The Client\'s service type & rate type applied to the job',
        calculation: ''
      },
      {
        name: 'Rate (Client)',
        description: 'The Client\'s rate ($) applied to the job.',
        calculation: ''
      },
      {
        name: 'Start (Client)',
        description: 'The client chargeable start time on the job based on the Job Pricing / Review process or the event times on the job if the job is not hourly.',
        calculation: 'Event time (if not time-based rate) or edited billable time.'
      },
      {
        name: 'End (Client)',
        description: 'The client chargeable end time on the job based on the Job Pricing / Review process or the event times on the job if the job is not hourly.',
        calculation: 'Event time (if not time-based rate) or edited billable time.'
      },
      {
        name: 'Break (Client)',
        description: 'The drivers break duration applied to the client.',
        calculation: ''
      },
      {
        name: 'Hrs (Client)',
        description:
        'The total chargeable hours worked for the client.',
      calculation:
        'A sum of (finish event time - started event time) or edited billable time.'
      },
      {
        name: 'Fl. ID',
        description:
        'The identifier for the Fleet Asset ID assigned to the job.',
      calculation:
        ''
      },
      {
        name: 'Serv (Fleet Asset)',
        description: 'The fleet assets service type and rate type applied to the job',
        calculation: ''
      },
      {
        name: 'Rate (Fleet Asset)',
        description: 'The fleet assets rate ($) associted with the job.',
        calculation: ''
      },
      {
        name: 'Start (Fleet Asset)',
        description: 'The Fleet Asset chargeable start time on the job based on the Job Pricing / Review process or the event times on the job if the job is not hourly.',
        calculation: 'Event time or edited billable time.'
      },
      {
        name: 'End (Fleet Asset)',
        description: 'The Fleet Asset chargeable end time on the job based on the Job Pricing / Review process or the event times on the job if the job is not hourly.',
        calculation: 'Event time or edited billable time.'
      },
      {
        name: 'Break (Fleet Asset)',
        description: 'The drivers break duration.',
        calculation: ''
      },
      {
        name: 'Hrs (Fleet Asset)',
        description:
        'The total chargeable hours worked for the Fleet Asset.',
      calculation:
        'A sum of (finish event time - started event time) or edited billable time.'
      },
      {
        name: 'Chrg',
        description:
        'The Client\'s freight charge excluding GST',
      calculation:
        ''
      },
      {
        name: 'Pay',
        description:
        'The fleet assets pay excluding GST',
      calculation:
        ''
      },
      {
        name: 'Mgn ($)',
        description:
        'The total margin in dollars.',
      calculation:
        'Chrg - Pay'
      },
      {
        name: 'Mgn (%)',
        description:
        'The total margin as percent.',
      calculation:
        '((Chrg - Pay)  / Chrg) * 100'
      },
      {
        name: 'Pay (Equipment Hire)',
        description:
        'The equipment hire charge estimate on the job.',
      calculation:
        'Derived from billable hours and equipment hire hourly rate.'
      },
      {
        name: 'Pay (Equipment Hire)',
        description:
        'The equipment hire charge estimate on the job.',
      calculation:
        ''
      },
      {
        name: 'Mgn ($) (Equipment Hire)',
        description:
        'Chrg - (Pay + Equipment Hire Pay)',
      calculation:
        ''
      },
      {
        name: 'Mgn (%) (Equipment Hire)',
        description:
        'The equipment hire charge estimate on the job.',
      calculation:
        '((Chrg - (Pay + Equipment Hire Pay))  / Chrg) * 100'
      }
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Branch Manager KPI',
    reportType: ReportType.BRANCH_MANGER_KPI,
    endPoint: 'generateReport/branchManagerKpi',
    description:
      'The Branch Manager KPI report offers a daily breakdown of revenue, costs, fuel charges, and invoice adjustments for all jobs in the specified date range, providing an essential tool for tracking performance on a day-to-day basis. Unlike the Margin Report, this report focuses on a broader perspective, considering revenue, cost, fuel charges, and invoice adjustments across each date.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
      'Every Job, regardless of status.',
      'Client, subcontractor and equipment hire adjustment ledger items, where the week-ending date is between the provided date range.',
    ],
    fileName: [
      ReportType.BRANCH_MANGER_KPI.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Date',
        description: 'The date that calculations will be made against.',
        calculation: '',
      },
      {
        name: 'Revenue',
        description:
          'The total revenue excluding GST for all jobs on the date.',
        calculation:
          'Fuel will be included if include fuel parameter is checked.',
      },
      {
        name: 'Costs',
        description: 'The total cost excluding Gst for all jobs in the date',
        calculation:
          'Fuel will be included if include fuel paramter is checked.',
      },
      {
        name: 'Margin ($)',
        description: 'The total margin for the date in dollars.',
        calculation: 'Revenue - Costs',
      },
      {
        name: 'Margin (%)',
        description: 'the total margin for the date as percentage.',
        calculation: '((Revenue - Costs)  / Revenue) * 100',
      },
      {
        name: 'Total Jobs',
        description: 'The total number of priced jobs.',
        calculation: 'Jobs that are in a status of Reviewed or Finalised.',
      },
      {
        name: 'Revenue Per Job',
        description: 'The average revenue made per job on the date.',
        calculation: 'Revenue / Total Jobs',
      },
      {
        name: 'Total Users',
        description:
          'The total number of clients that had jobs booked for the date.',
        calculation: '',
      },
      {
        name: 'Revenue Per User',
        description: 'The average revenue per client.',
        calculation: 'Total Users / Revenue',
      },
      {
        name: 'Cancelled',
        description: 'The total number of cancelled jobs on the date.',
        calculation: '',
      },
      {
        name: 'Service Failures',
        description:
          'The total number of jobs that were defined as a service failure.',
        calculation: '',
      },
      {
        name: 'Not Yet Priced',
        description: 'Jobs that are yet to be priced on the date.',
        calculation: '',
      },
      {
        name: 'Invoice Adjustment Revenue',
        description:
          'The total revenue generated from client invoice adjustments.',
        calculation: '',
      },
      {
        name: 'Invoice Adjustment Cost',
        description:
          'The total cost generated from subcontractor invoice adjustments.',
        calculation: '',
      },
      {
        name: 'Invoice Adjustment - Revenue Count',
        description: 'The total number of client adjustments.',
        calculation: '',
      },
      {
        name: 'Invoice Adjustment - Cost Count',
        description: 'The total number of subcontractor adjustments.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Sales By Client',
    reportType: ReportType.SALES_BY_CLIENT,
    endPoint: 'generateReport/getSalesByClientReport',
    description:
      'The "Sales by Client" report offers dynamic revenue analysis, allowing you to define date ranges by selecting the number of weeks to run and specifying weeks per column. With client names in the first column, it provides a client-centric view of total revenue for each date range. The last column sums up the revenue, offering a comprehensive overview of your earnings. This flexible tool empowers you to make data-driven decisions and gain valuable insights into your sales performance.',
    audience: 'The report is intended for internal use.',
    query: [
      'Client ledger items where the week-ending date is between the provided date range.',
    ],
    fileName: [
      ReportType.SALES_BY_CLIENT.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Client',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: '[Start Date] - [End Date]',
        description: 'The Revenue generated between the columns date range.',
        calculation: 'Pulled from ledger items.',
      },
      {
        name: 'TOTAL',
        description: 'The total sum of all previous columns.',
        calculation: '',
      },
    ],
  },
  // prettier-ignore
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Rank Sales By Revenue',
    reportType: ReportType.RANK_SALES_BY_REVENUE,
    endPoint: 'generateReport/getRankSalesByRevenueReport',
    description: 'The Rank Sales By Revenue report is a powerful tool that ranks your clients in ascending order based on the revenue they generated within the specified date range parameters. It provides a clear and objective snapshot of client performance, helping you identify your top revenue contributors. Additionally, the report captures the last known date each client engaged in work, providing valuable insights into client activity. With this report, you can make informed decisions to strengthen client relationships and enhance your business\'s revenue streams.',
    audience: 'The report is intended for internal use.',
    query: ['Jobs that were booked to be completed within the provided date range.',
    'Jobs that are either Invoiced OR a status of Reviewed and Finalised. Dictated by whether the include only invoiced jobs parameter is checked.'],
    fileName: [
      ReportType.RANK_SALES_BY_REVENUE.toString(),
      'report',
      'company',
      'division',
      'runDate'
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER
    ],
    columns: [
      {
        name: 'Rank',
        description: 'The ranking position for the client.',
        calculation: 'Ascending order.'
      },
      {
        name: 'Code',
        description: 'The Client\'s Identifier',
        calculation: ''
      },
      {
        name: 'Name',
        description: 'The Client\'s name',
        calculation: ''
      },
      {
        name: 'Revenue $',
        description: 'Total revenue minus fuel excluding GST along with the number of jobs.',
        calculation: ''
      },
      {
        name: 'Revenue %',
        description: 'The revenue percentage generated against the total revenue for all client\'s.',
        calculation: ''
      },
      {
        name: 'Cost $',
        description: 'Total cost minus fuel excluding GST',
        calculation: ''
      },
      {
        name: 'Margin $',
        description: 'The margin generated against all of the clients jobs.',
        calculation: 'Revenue - Cost'
      },
      {
        name: 'Margin %',
        description: 'The margin as a percentage.',
        calculation: '((Revenue - Cost)  / Revenue) * 100'
      },
      {
        name: 'Fuel Revenue',
        description: 'The total revenue generated by the clients fuel levy.',
        calculation: ''
      },
      {
        name: 'Fuel Cost',
        description: 'The total cost generated by the fleet assets fuel levy.',
        calculation: ''
      },
      {
        name: 'Last Job',
        description: 'The last known date the client executed work.',
        calculation: ''
      }
    ]
  },
  // prettier-ignore
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Rank Sales By Revenue By Sales Person',
    reportType: ReportType.RANK_SALES_BY_REVENUE_BY_SALES_PERSON,
    endPoint: 'generateReport/getRankSalesByRevenueBySalesPersonReport',
    description: 'The Rank Sales By Revenue By Sales Person report is a tool for evaluating the performance of a salesperson\'s clients across the whole company. It ranks clients in ascending order based on the revenue generated during a specified date range. Importantly, the report dynamically considers salesperson commission dates. If a salesperson\'s commission start and end dates partially overlap with the selected date range, the revenue calculations align with the commission dates. This approach provides a precise view of each client\'s revenue contribution within the context of salesperson commission terms.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs across all divisions that were booked to be completed within the provided date range.',
      'Jobs that are either Invoiced OR a status of Reviewed and Finalised. Dictated by whether the include only invoiced jobs parameter is checked.'],
    fileName: [
      ReportType.RANK_SALES_BY_REVENUE_BY_SALES_PERSON.toString(),
      'report',
      'company',
      'salesperson',
      'runDate'
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER
    ],
    columns: [
      {
        name: 'Rank',
        description: 'The ranking position for the client.',
        calculation: 'Ascending order.'
      },
      {
        name: 'Client ID',
        description: 'The clients unique identifier.',
        calculation: ''
      },
      {
        name: 'Name',
        description: 'The client\'s name.',
        calculation: ''
      },
      {
        name: 'Commission Start',
        description:
          'The start date of the commission period for the salesperson and client.',
        calculation: ''
      },
      {
        name: 'Commission End',
        description: 'The end date of the commission period for the salesperson and client.',
        calculation: ''
      },
      {
        name: 'Partial Date Overlap',
        description:
          'Indicates whether the commission date range partially overlaps with the start and end date parameters.',
        calculation:
          'Commision Start Date > Report start Date or Commission End Date < Report End Date'
      },
      {
        name: 'Revenue $',
        description: 'The total revenue in dollars, excluding GST and fuel costs.',
        calculation:
          'The total revenue generated from work performed between the Commission Start and Commission End dates.'
      },
      {
        name: 'Revenue %',
        description:
          'The percentage of revenue generated in relation to the total revenue from all clients.',
        calculation: ''
      },
      {
        name: 'Cost $',
        description:
          'The total cost in dollars for the client, excluding GST and fuel expenses.',
        calculation: ''
      },
      {
        name: 'Margin $',
        description: 'The profit margin generated from all of the client\'s jobs.',
        calculation: 'Revenue $ - Cost $'
      },
      {
        name: 'Margin %',
        description: 'The margin represented as a percentage.',
        calculation: '((Revenue $ - Cost $)  / Revenue $) * 100'
      },
      {
        name: 'Fuel Revenue $',
        description: 'The total fuel revenue, excluding GST.',
        calculation: ''
      },
      {
        name: 'Fuel Cost $',
        description: 'The total fuel cost, excluding GST.',
        calculation: ''
      },
      {
        name: 'Last Job',
        description: 'The most recent date on which the client executed work.',
        calculation: ''
      }
    ]
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Driver Pay',
    reportType: ReportType.DRIVER_PAY,
    endPoint: 'generateReport/getDriverPayReport',
    description:
      'The Driver Pay report provides a list of all jobs completed during the selected period, with each job detailed as a separate line item. For each job, the report highlights the assigned driver and their associated costs, offering you a clear and detailed overview of driver remuneration.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Reviewed or Finalised.',
    ],
    fileName: [
      ReportType.DRIVER_PAY.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Fleet Asset',
        description:
          'The identifier for the Fleet Asset ID assigned to the job.',
        calculation: ''
      },
      {
        name: 'Driver',
        description: 'The driver\'s name assigned to the job.',
        calculation: ''
      },
      {
        name: 'Job #',
        description: 'A unique identifier for each job.',
        calculation: ''
      },
      {
        name: 'Freight Charge',
        description: 'Freight charge, excluding GST.',
        calculation: ''
      },
      {
        name: 'Freight Adjustment',
        description: 'Freight adjustment charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Outside Metro',
        description: 'Outside Metro charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Standby',
        description: 'Standby charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Demurrage',
        description: 'Demurrage charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Fuel Surcharge',
        description: 'Fuel charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Additional Charges',
        description: 'Additional charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Tolls',
        description: 'Toll charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Gst',
        description: 'The total Goods and Services Tax (GST) charge.',
        calculation: ''
      },
      {
        name: 'Total',
        description: 'The total charge.',
        calculation: ''
      }
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Sales By Sales Person',
    reportType: ReportType.SALES_BY_SALES_PERSON,
    endPoint: 'generateReport/salesBySalesPerson',
    // prettier-ignore
    description: 'The Sales by Sales Person report is a comprehensive tool designed to provide a detailed analysis of revenue generated by individual salespersons within the specified timeframe. The report\'s primary focus is to present a visual representation of revenue data across 52 weeks, starting from the chosen commencement date. Each column within the report corresponds to a specific week, offering a granular view of sales performance. The report also includes a breakdown of revenue for both the current budget year and the previous budget year, recognizing that commission terms for the salesperson may have commenced prior to the chosen start date but continued into the current budget year. This comprehensive approach ensures that users can effectively track and analyze sales performance while accommodating variations in commission terms and fiscal periods.',
    audience: 'The report is intended for internal use.',
    query: [
      'Clients that are associated with the selected salesperson.',
      'Jobs across all divisions that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Finalised.',
    ],
    fileName: [
      ReportType.SALES_BY_SALES_PERSON.toString(),
      'report',
      'company',
      'salesperson',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Client Name',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'Division',
        description: 'The division the client is associated with.',
        calculation: '',
      },
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Sales Start Date',
        description:
          'The commission start date for the sales person and client.',
        calculation: '',
      },
      {
        name: 'Sales End Date',
        description: 'The commission end date for the sales person and client.',
        calculation: '',
      },
      {
        name: 'Week',
        description:
          'These columns represent the weekly time intervals, each covering a seven-day period, beginning from the selected start date and spanning across 52 weeks.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Delivery Performance',
    reportType: ReportType.DELIVERY_PERFORMANCE,
    endPoint: 'generateReport/deliveryPerformance',
    // prettier-ignore
    description: 'The Delivery Performance report is a powerful tool for evaluating job arrival time performance and allows the user to look at it from a detailed job perspective, date perspective or client perspective. It focuses on jobs that deviate from their expected arrival time compared to the report\'s performance duration parameter. The report separates jobs into whether they were designated as ASAP or for a specific time (marked as Estimate). This report provides you with the ability to assess performance against expected time of arrival and actual time of arrival, helping you make informed decisions to enhance your operations.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Completed, Reviewed or Finalised.',
    ],
    fileName: [
      ReportType.DELIVERY_PERFORMANCE.toString(),
      'report',
      'company',
      'division',
      'GroupType',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Booked For / Date',
        description:
          'The date of the job or jobs, depending on the grouping type.',
        calculation: '',
      },
      {
        name: 'Arrived At',
        description: 'The arrival time of the first leg on the job.',
        calculation: 'Column available when no grouping is selected.',
      },
      {
        name: 'Client Name',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Job Id',
        description: 'The jobs unique identifier.',
        calculation: 'Column available when no grouping is selected.',
      },
      {
        name: 'Time Definition',
        description:
          'Indicated whether the time type on the job is "ESTIMATE" or "ASAP."',
        calculation: 'Column available when no grouping is selected.',
      },
      {
        name: 'Exceeded Duration (mins)',
        description:
          'The variance between the defined arrival time and the actual arrival time.',
        calculation: 'Column available when no grouping is selected.',
      },
      {
        name: 'Service Failure',
        description:
          'Indicates whether the job was marked as a service failure.',
        calculation: 'Column available when no grouping is selected.',
      },
      {
        name: 'Total Job Count',
        description:
          'The total count of jobs that occurred on the specified date.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'Cancelled Jobs',
        description:
          'The total count of jobs that were canceled on the specified date.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ASAP - Total Job Count',
        description:
          'The total count of jobs that were designated as "ASAP" time.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ASAP - Under Performed',
        description:
          'The variance between the expected and actual arrival times in comparison to the performance duration parameter for jobs categorized as ASAP',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ASAP - Average Exceeded Duration (mins)',
        description:
          'The average count of "ASAP" timed jobs that exceeded the expected performance duration.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ASAP - Performance (%)',
        description:
          'The percentage of "ASAP" timed jobs that exceeded the expected performance duration.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ON TIME - Total Job Count',
        description:
          'The total count of jobs that were designated as "ESTIMATE" time.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ON TIME - Under Performed',
        description:
          'The variance between the expected and actual arrival times in comparison to the performance duration parameter for jobs categorized as ESTIMATE',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ON TIME - Average Exceeded Duration (mins)',
        description:
          'The average count of "ESTIMATE" timed jobs that exceeded the expected performance duration.',
        calculation: 'Column available when grouping type is selected.',
      },
      {
        name: 'ON TIME - Performance (%)',
        description:
          'The percentage of "ESTIMATE" timed jobs that exceeded the expected performance duration.',
        calculation: 'Column available when grouping type is selected.',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Service Totals',
    reportType: ReportType.SERVICE_TOTALS,
    endPoint: 'generateReport/serviceTotals',
    description:
      'The Service Totals report provides a comprehensive view of jobs categorized by their respective service rate types, offering insights into financial and operational aspects for each service type. The report includes columns detailing the service type, the number of jobs, total revenue, total cost, margin (both in dollars and as a percentage), and the total time spent on each service rate type. This report is a valuable resource for assessing financial performance, profitability, and resource allocation across different service categories, aiding in data-driven decision-making and strategic planning.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
      'Jobs that are in a status of Completed, Reviewed or Finalised.',
    ],
    fileName: [
      ReportType.SERVICE_TOTALS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Service Type',
        description: 'The name of the service type & rate type.',
        calculation: '',
      },
      {
        name: '# of Jobs',
        description:
          'The total count of jobs associated with the service type & rate type.',
        calculation: '',
      },
      {
        name: 'Total Revenue',
        description:
          'The total revenue generated by the service type & rate type.',
        calculation: '',
      },
      {
        name: 'Total Cost',
        description:
          'The total cost generated by the service type & rate type.',
        calculation: '',
      },
      {
        name: 'Margin ($)',
        description:
          'The total margin in dollars generated by the service type & rate type.',
        calculation: '',
      },
      {
        name: 'Margin ($)',
        description:
          'The overall margin, represented as a percentage, generated by the service type & rate type.',
        calculation: '',
      },
      {
        name: 'Total Time (mins)',
        description:
          'The total billed duration as defined on the jobs during the pricing process.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Service Failures',
    reportType: ReportType.SERVICE_FAILURES,
    endPoint: 'generateReport/serviceFailures',
    // prettier-ignore
    description: 'The Service Failures report offers a comprehensive overview of jobs that have been either actioned as service failures or cancelled. Each job is presented on a separate line, allowing for a detailed review of the reasons behind service failures or cancellations. In addition to job-specific details, the report includes notes pertaining to the service failure or cancellation, along with information about the user responsible for these actions. This report provides a thorough analysis of the factors contributing to service issues, making it a valuable resource for improving service quality and operational efficiency.',
    audience: 'The report is intended for internal use.',
    query: [],
    fileName: [
      ReportType.SERVICE_FAILURES.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Date',
        description: 'The date on which each job was booked to be completed.',
        calculation: '',
      },
      {
        name: 'JobId',
        description: 'A unique identifier for each job.',
        calculation: '',
      },
      {
        name: 'Client',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Vehicle',
        description: 'Identifies the specific service type of the job.',
        calculation: '',
      },
      {
        name: 'Service Failure',
        description:
          'Indicates whether the job was marked as a service failure.',
        calculation: '',
      },
      {
        name: 'Service Failure date',
        description: 'The date when the service failure occurred.',
        calculation: '',
      },
      {
        name: 'Service Failure By',
        description:
          'The user who took action in response to the service failure.',
        calculation: '',
      },
      {
        name: 'Service Failure comment',
        description: 'The note related to the service failure.',
        calculation: '',
      },
      {
        name: 'Cancelled',
        description: 'Indicates whether the job was cancelled.',
        calculation: '',
      },
      {
        name: 'Cancelled date',
        description: 'The date when the cancellation occurred.',
        calculation: '',
      },
      {
        name: 'Cancelled By',
        description: 'The user who carried out the cancellation.',
        calculation: '',
      },
      {
        name: 'Cancelled Comment',
        description: 'The note linked to the cancellation.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Operator Performance',
    reportType: ReportType.OPERATOR_PERFORMANCE,
    endPoint: 'generateReport/operatorPerformance',
    // prettier-ignore
    description:
      'The Operator Performance Report provides an overview of each operations users\' actions performed (including booking, import reviews, allocation, leg additions, pricing, and finalization). Notably, the report offers flexibility through a Group Days checkbox, enabling users to choose between grouping all days together or viewing them separately. It\'s important to note that a 10-day buffer is added to ensure that jobs booked outside of the date range but with events actioned within the date range are included in the analysis. By summarizing individual event counts, the report empowers thorough evaluations of operator productivity and efficiency, serving as a valuable tool in optimizing the operations teams\' performance',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were booked to be completed within the provided date range.',
    ],
    fileName: [
      ReportType.REMITTANCE_SUMMARY.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Operator Name / Hour Range',
        description:
          'Indicates the hour range within which the events took place for each row.',
        calculation: '',
      },
      {
        name: 'Booked',
        description:
          'Indicates the count of booking events actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Import Reviews',
        description:
          'Indicates the count of reviewed job events actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Allocated',
        description:
          'Indicates the count of allocation events actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Legs Added',
        description:
          'Indicates the count of leg added events actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Reviewed',
        description:
          'Indicates the count of reviewed events actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Finalised',
        description:
          'Indicates the count of finalised events that were actioned by the operator.',
        calculation: '',
      },
      {
        name: 'Total Actions',
        description: 'The total sum of all events actioned by the operator.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Remittance Summary',
    reportType: ReportType.REMITTANCE_SUMMARY,
    endPoint: 'generateReport/rctiSummary',
    // prettier-ignore
    description:
      'The Remittance Summary Report provides details on the Recipient Created Tax Invoices (RCTI) / Subcontractor Payments processed within GoDesta during the specified date range. Users can query based on various date types, including the week ending date, due date, and date sent. The report showcases key RCTI / Subcontractor Payment details such as the unique invoice number, recipient\'s unique identifier and name, trading terms, designated week-ending date, due date, and financial breakdown. The financial breakdown includes total freight revenue (excluding GST), additional charges, fuel levy revenue, GST, and the total remitted amount, inclusive of GST. Additionally, a totals section at the bottom of the report provides a sum of all RCTI / Subcontractor Payments, ensuring a comprehensive view of financial transactions for informed decision-making and financial management.',
    audience: 'The report is intended for internal use.',
    query: [
      'subcontractor Recipient Created Tax Invoices and subcontractor adjustments with the selected date type falling within the specified date range.',
    ],
    fileName: [
      ReportType.REMITTANCE_SUMMARY.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Invoice #',
        description:
          'The unique number associated with the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'Recipient ID',
        description: 'The subcontractors unique identifier.',
        calculation: '',
      },
      {
        name: 'Recipient Name',
        description: 'The subcontractors name.',
        calculation: '',
      },
      {
        name: 'Trading Terms',
        description: 'The subcontractors trading terms.',
        calculation: '',
      },
      {
        name: 'W/E Date',
        description:
          'The week ending date designated for the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'Due Date',
        description:
          'The due date specified for the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'Freight',
        description: 'The total freight revenue, excluding GST.',
        calculation: 'Freight charge + Standby charge + demurrage charge',
      },
      {
        name: 'Add. Chg',
        description: 'Additional charges associated with job execution.',
        calculation: '',
      },
      {
        name: 'Fuel Levy',
        description:
          'The total revenue generated by the subcontractors fuel levy.',
        calculation: '',
      },
      {
        name: 'RCTI Adj.',
        description:
          'Overall adjustments made to the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'GST',
        description:
          'The total GST associated with the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'adj. Chg',
        description:
          'Overall adjustments made to the RCTI / Subcontractor Payments, excluding GST.',
        calculation: '',
      },
      {
        name: 'adj. GST',
        description:
          'Overall Adjustments in GST applied to the RCTI / Subcontractor Payments.',
        calculation: '',
      },
      {
        name: 'Remitted',
        description: 'The total remitted amount, including GST.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Invoice Summary',
    reportType: ReportType.INVOICE_SUMMARY,
    endPoint: 'generateReport/invoiceSummary',
    // prettier-ignore
    description:       'The Invoice Summary Report provides details on the Client Invoices processed within GoDesta during the specified date range. Users can query based on various date types, including the week ending date, due date, and date sent. The report showcases key Client Invoice details such as the unique invoice number, recipient\'s unique identifier and name, trading terms, designated week-ending date, due date, and financial breakdown. The financial breakdown includes total freight revenue (excluding GST), additional charges, fuel levy revenue, GST, and the total invoiced amount, inclusive of GST. Additionally, a totals section at the bottom of the report provides a sum of all Client Invoices, ensuring a comprehensive view of financial transactions for informed decision-making and financial management.',
    audience: 'The report is intended for internal use.',
    query: [
      'Client invoices and client invoice adjustments with the selected date type falling within the specified date range.',
    ],
    fileName: [
      ReportType.INVOICE_SUMMARY.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Invoice #',
        description: 'The unique number associated with the invoice.',
        calculation: '',
      },
      {
        name: 'Recipient ID',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Recipient Name',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'Trading Terms',
        description: 'The clients trading terms.',
        calculation: '',
      },
      {
        name: 'W/E Date',
        description: 'The week ending date designated for the invoice.',
        calculation: '',
      },
      {
        name: 'Due Date',
        description: 'The due date specified for the invoice.',
        calculation: '',
      },
      {
        name: 'Freight',
        description: 'The total freight revenue, excluding GST.',
        calculation: 'Freight charge + Standby charge + demurrage charge',
      },
      {
        name: 'Add. Chg',
        description: 'Additional charges associated with job execution.',
        calculation: '',
      },
      {
        name: 'Fuel Levy',
        description: 'The total revenue generated by the clients fuel levy.',
        calculation: '',
      },
      {
        name: 'GST',
        description: 'The total GST associated with the invoice.',
        calculation: '',
      },
      {
        name: 'Invoiced',
        description: 'The total invoiced amount, including GST.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Invoice Finance Summary',
    reportType: ReportType.INVOICE_FINANCES_SUMMARY,
    endPoint: 'generateReport/getInvoiceFinanceSummaryReport',
    // prettier-ignore
    description: 'The Invoice Finance Summary report is used to upload Client Invoice totals to the Transport Operators respective banking portal.',
    audience: 'The report is intended for internal use.',
    query: [
      'Invoices that have a week ending date equal to the provided date parameter.',
      'Cash Sale invoices are excluded from this report.',
    ],
    fileName: [
      ReportType.OPERATIONAL_KPI.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Date',
        description: 'The week ending date of the invoice.',
        calculation: '',
      },
      {
        name: 'Invoice Number',
        description: 'The unique number associated with the invoice.',
        calculation: '',
      },
      {
        name: 'Client Name',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'Revenue',
        description: 'The total invoiced amount including GST.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Operational KPI',
    reportType: ReportType.OPERATIONAL_KPI,
    endPoint: 'generateReport/operationalKpi',
    // prettier-ignore
    description: 'The Operational KPI report is a powerful tool that delivers comprehensive financial insights within a specified date range for all invoiced jobs. It presents key financial data, including total revenue, costs, equipment hire costs, tolls, fuel-related revenue and costs, invoiced revenue and costs, and adjustments. This report not only provides an overview of financial performance but also highlights critical operational metrics, such as the count of invoiced jobs, cancelled jobs, service failures, and unique client and national client identifiers. Whether you\'re analyzing the financial health of your operations or monitoring key performance indicators, this report offers valuable data for informed decision-making and improved operational efficiency.',
    audience: 'The report is intended for internal use.',
    query: [
      'Jobs that were cancelled within the specified date range.',
      'Ledger items that were processed within the specified date range.',
      'Jobs that were associated with the ledger items.',
    ],
    fileName: [
      ReportType.OPERATIONAL_KPI.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Start Date',
        description: 'The report\'s start date.',
        calculation: ''
      },
      {
        name: 'End Date',
        description: 'The report\'s end date.',
        calculation: ''
      },
      {
        name: 'Revenue',
        description:
          'The total revenue from freight, demurrage, standby, tolls, and additional charges, excluding GST.',
        calculation: ''
      },
      {
        name: 'Costs',
        description:
          'The total cost of freight, demurrage, standby, tolls, additional charges and equipment hire, excluding GST.',
        calculation: ''
      },
      {
        name: 'Equipment Hire Costs',
        description: 'The total cost related to equipment hire.',
        calculation: ''
      },
      {
        name: 'Tolls Revenue',
        description: 'The total revenue generated from tolls, excluding GST.',
        calculation: ''
      },
      {
        name: 'Tolls Cost',
        description: 'The total cost generated from tolls, excluding GST.',
        calculation: ''
      },
      {
        name: 'Fuel Revenue',
        description: 'The total revenue generated from fuel, excluding GST.',
        calculation: ''
      },
      {
        name: 'Fuel Cost',
        description: 'The total cost generated from fuel, excluding GST.',
        calculation: ''
      },
      {
        name: 'Total Invoiced',
        description:
          'The total revenue generated from invoicing, excluding GST.',
        calculation: ''
      },
      {
        name: 'Total RCTI',
        description: 'The total cost generated from RCTI, excluding GST.',
        calculation: ''
      },
      {
        name: 'Invoice Adjustment Revenue',
        description:
          'The total revenue generated from client invoice adjustments.',
        calculation: ''
      },
      {
        name: 'Invoice Adjustment Cost',
        description: 'The total cost generated from subcontractor invoice adjustments.',
        calculation: ''
      },
      {
        name: 'Total Jobs',
        description: 'The total count of jobs that have been invoiced.',
        calculation: ''
      },
      {
        name: 'Cancelled',
        description: 'The total count of jobs that were cancelled.',
        calculation: ''
      },
      {
        name: 'Service Failures',
        description: 'The total count of jobs with a service failure.',
        calculation: 'Count includes invoiced and cancelled jobs.'
      },
      {
        name: 'Total Users',
        description: 'The total count of client\'s invoiced.',
        calculation: ''
      },
      {
        name: 'National Client ID Users',
        description: 'The total count of clients associated with a national client.',
        calculation: ''
      },
      {
        name: 'Unique National Clients',
        description: 'The total count of unique national client\'s',
        calculation: ''
      },
      {
        name: 'Cash Sale Users',
        description: 'The total count of invoiced jobs that were performed as a cash sale.',
        calculation: ''
      },
      {
        name: 'Invoice Adjustment - Revenue Count',
        description: 'The total number of client adjustments.',
        calculation: ''
      },
      {
        name: 'Invoice Adjustment - Cost Count',
        description: 'The total number of subcontractor adjustments.',
        calculation: ''
      }
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Subcontractor Bank Details',
    reportType: ReportType.SUBCONTRACTOR_BANK_DETAILS,
    endPoint: 'generateReport/getSubcontractorBankDetailsReport',
    // prettier-ignore
    description:
      'The Subcontractor Bank Details allows you to quickly review and check bank details associated with Subcontractors. It\'s a valuable tool for ensuring smooth financial interactions with your subcontractor network.',
    audience: 'The report is intended for internal use.',
    query: ['Subcontractors that are currently active.'],
    fileName: [
      ReportType.SUBCONTRACTOR_BANK_DETAILS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Name',
        description: 'The subcontractor company name.',
        calculation: '',
      },
      {
        name: 'Account Name',
        description:
          'The name of the bank account that is on record for the subcontractor.',
        calculation: '',
      },
      {
        name: 'BSB Number',
        description: 'The BSB Number that is on record for the subcontractor.',
        calculation: '',
      },
      {
        name: 'Account Number',
        description:
          'The bank account number that is on record for the subcontractor.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Subcontractor Compliance',
    reportType: ReportType.SUBCONTRACTOR_COMPLIANCE,
    endPoint: 'generateReport/getSubcontractorComplianceReport',
    // prettier-ignore
    description:
      'The Subcontractor Compliance Report provides a comprehensive overview of your subcontractors and their insurance details. This report covers various aspects of subcontractor insurance, including policy types, coverage details, expiration dates, and other pertinent information. It serves as a crucial tool for ensuring that your subcontractors are fully compliant and adequately insured for their respective services. With this report, you can effectively track and manage insurance compliance for your subcontractor network, contributing to a more secure and well-protected business ecosystem.',
    audience: 'The report is intended for internal use.',
    query: ['Subcontractors that are currently active.'],
    fileName: [
      ReportType.SUBCONTRACTOR_COMPLIANCE.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Company Name',
        description:
          'The subcontractor\'s company name associated to the compliance record.',
        calculation: ''
      },
      {
        name: 'Insurance Provider',
        description:
          'The name of the Insurance Provider that is on record for the Subcontractor.',
        calculation: ''
      },
      {
        name: 'Insurance Type',
        description:
          'The insurance type that is on record for the Subcontractor - this can be Vehicle Insurance, Public Liability, Marine, & Workers Compensation.',
        calculation: ''
      },
      {
        name: 'Insurance Expiry',
        description:
          'The date that the insurance policy will expire that is on record for the Subcontractor.',
        calculation: ''
      },
      {
        name: 'Policy Number',
        description:
          'The insurance policy number that is on record for the Subcontractor.',
        calculation: ''
      }
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Fleet Asset Compliance',
    reportType: ReportType.FLEET_ASSET_COMPLIANCE,
    endPoint: 'generateReport/getSubcontractorComplianceReport',
    description:
      'The Fleet Asset Compliance Report offers a comprehensive view of your fleet assets and their registration details. This report covers various aspects of fleet registration, including registration numbers, expiration dates, and other relevant information. It serves as a vital tool for maintaining compliance and ensuring that your fleet assets are up-to-date and ready for operation. With this report, you can effectively track and manage the registration status of your fleet, contributing to safer and more efficient operations.',
    audience: 'The report is intended for internal use.',
    query: ['Drivers that are currently active.'],
    fileName: [
      ReportType.FLEET_ASSET_COMPLIANCE.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Fleet Asset ID',
        description:
          'The Fleet Asset ID that is assigned to the vehicle for the purposes of internal identification.',
        calculation: '',
      },
      {
        name: 'Registration',
        description: 'The Registration Identification number for the vehicle.',
        calculation: '',
      },
      {
        name: 'State',
        description: 'The state that the vehicle is registered in.',
        calculation: '',
      },
      {
        name: 'Expiry',
        description:
          'The date that is on record for when the registration is expected to expire.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Driver Compliance',
    reportType: ReportType.DRIVER_COMPLIANCE,
    endPoint: 'generateReport/getSubcontractorComplianceReport',
    // prettier-ignore
    description: 'The Driver Compliance report provides a comprehensive snapshot of active drivers, focusing on their associated licenses and inductions. This report encompasses the various license and induction types held by drivers, complete with their respective expiration dates. By offering a clear overview of driver compliance, this report ensures that your workforce operates within the necessary regulations and safety standards. It\'s an essential tool for effective driver management and compliance tracking, helping you maintain a compliant and reliable driver workforce.',
    audience: 'The report is intended for internal use.',
    query: ['Drivers that are currently active.'],
    fileName: [
      ReportType.DRIVER_COMPLIANCE.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Driver Name',
        description: 'The drivers name.',
        calculation: '',
      },
      {
        name: 'Type',
        description: 'The type of license or induction.',
        calculation: '',
      },
      {
        name: 'Expiry',
        description:
          'The date of expiration for the license or induction document.',
        calculation: '',
      },
      {
        name: 'Number',
        description: 'The unique document number or identifier.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Service Rates',
    reportType: ReportType.SERVICE_RATES,
    endPoint: 'generateReport/serviceRates',
    // prettier-ignore
    description: 'The Service Rates Report offers a comprehensive overview of each client\'s service rate agreements. It presents the client\'s latest trade dates, along with their customized service rate agreement and fallback default service rate card, each with specified valid-from-to dates. The report also highlights the fuel surcharge levy rate, providing a detailed view of each client\'s service rate structure. This report is an invaluable tool for ensuring clarity and accuracy in client billing, while helping you make informed decisions regarding service rates and agreements.',
    audience: 'The report is intended for internal use.',
    query: [],
    fileName: [
      ReportType.SERVICE_RATES.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    // prettier-ignore
    columns: [
      {
        name: 'Client Name',
        description: 'The clients name.',
        calculation: ''
      },
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: ''
      },
      {
        name: 'Credit Status',
        description: 'The clients current credit status.',
        calculation: ''
      },
      {
        name: 'Retired',
        description: 'Indicates whether the client is retired or not.',
        calculation: ''
      },
      {
        name: 'Last Invoiced Date',
        description: 'The last known invoice date.',
        calculation: 'The weekending date of the last known invoice.'
      },
      {
        name: 'Latest Booking Date',
        description: 'The most recent job date for the client.',
        calculation: ''
      },
      {
        name: 'Custom Rate Card',
        description: 'The name of the client\'s currently active custom rate card.',
        calculation: ''
      },
      {
        name: 'Custom Rate Card valid From',
        description: 'The date from which the client\'s custom rate card is valid.',
        calculation: ''
      },
      {
        name: 'Custom Rate Card Valid To',
        description: 'The date from which the client\'s custom rate card is valid to.',
        calculation: ''
      },
      {
        name: 'Default Rate Card',
        description: 'The name of the client\'s currently active default rate card.',
        calculation: ''
      },
      {
        name: 'Default Rate Card Valid From',
        description: 'The date from which the client\'s default rate card is valid.',
        calculation: ''
      },
      {
        name: 'Default Rate Card Valid To',
        description: 'The date from which the client\'s default rate card is valid to.',
        calculation: ''
      },
      {
        name: 'Fuel Levy (%)',
        description: 'The current fuel surcharge levy for the client.',
        calculation: ''
      },
      {
        name: 'Fuel Levy Valid From',
        description: 'The date from which the client\'s fuel surcharge levy is valid.',
        calculation: ''
      },
      {
        name: 'Fuel Levy Valid To',
        description: 'The date from which the client\'s fuel surcharge levy is valid to.',
        calculation: ''
      }
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Client Details Summary',
    reportType: ReportType.CLIENT_DETAILS_SUMMARY,
    endPoint: 'generateReport/clientDetailsSummary',
    description:
      'The Client Details Summary report serves as a valuable resource for accessing essential information about clients in your system. It includes contact details such as phone numbers and email addresses, along with their respective six month revenue. Users have the flexibility to customize the report by opting to include retired clients, although by default, retired clients are excluded. This feature ensures that you can easily manage and reach out to your active clients while still having the option to access contact information for retired clients when needed. This report simplifies client administration and facilitates communication and tracking within your client network.',
    audience: 'The report is intended for internal use.',
    // prettier-ignore
    query: [
    'Clients that belong to the users company and division.',
    'All Clients will be included if no specific clients are selected.',
    'Retired clients will not be included unless \'include Retired\' is checked.',
    'Jobs that have been finalised within the past six months.'
  ],
    fileName: [
      ReportType.CLIENT_DETAILS_SUMMARY.toString(),
      'reports',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Client Name',
        description: 'The clients name.',
        calculation: '',
      },
      {
        name: 'National Client Name',
        description: 'The national client name.',
        calculation: 'Whether the client is associated with a national client.',
      },
      {
        name: 'Type',
        description: 'Whether the client is permanent or adhoc.',
        calculation: 'Client Type defined in the clients key details.',
      },
      {
        name: 'Address',
        description: 'The location address for the client.',
        calculation: '',
      },
      {
        name: 'Suburb',
        description: 'The location suburb for the client.',
        calculation: '',
      },
      {
        name: 'State',
        description: 'The location state for the client.',
        calculation: '',
      },
      {
        name: 'Postcode',
        description: 'The location postcode for the client.',
        calculation: '',
      },
      {
        name: 'Dispatcher Email',
        description: 'The clients default dispatcher email address.',
        calculation: '',
      },
      {
        name: 'Reports Email',
        description: 'The clients default reports email address.',
        calculation: '',
      },
      {
        name: 'Accounts Email',
        description: 'The clients default accounts email address.',
        calculation: '',
      },
      {
        name: 'Account Management Email',
        description: 'The clients default account management email address.',
        calculation: '',
      },
      {
        name: 'Accounts Payable Email',
        description: 'The clients default accounts payable email address.',
        calculation: '',
      },
      {
        name: 'Trading Terms',
        description: 'The clients trading terms.',
        calculation: '',
      },
      {
        name: 'Commencement Date',
        description: 'The date the client commenced work.',
        calculation: '',
      },
      {
        name: 'Last Trade Date',
        description: 'The last known trade date for the client.',
        calculation: '',
      },
      {
        name: 'Revenue Excl Fuel Excl Gst (6 Months)',
        description:
          'The total revenue excluding GST generated by the client in the last six months.',
        calculation: 'The sum of the last six months revenue - fuel.',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Client Contacts',
    reportType: ReportType.CLIENT_CONTACTS,
    endPoint: 'generateReport/clientContacts',
    description:
      'The Client Contacts report serves as a valuable resource for accessing essential information about client contacts in your system. Users have the flexibility to customize the report by opting to include retired clients, although by default, retired clients are excluded. This feature ensures that you can easily manage and reach out to your active client contacts while still having the option to access contact information for retired client contacts when needed. This report simplifies client administration and facilitates communication and tracking within your clients network.',
    audience: 'The report is intended for internal use.',
    // prettier-ignore
    query: [
    'Clients that belong to the users company and division.',
    'All Clients will be included if no specific clients are selected.',
    'Retired clients will not be included unless \'include Retired\' is checked.'
  ],
    fileName: [
      ReportType.CLIENT_CONTACTS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Client Id',
        description: 'The clients unique identifier.',
        calculation: '',
      },
      {
        name: 'Client Name',
        description: 'The clients name the contact is associated with.',
        calculation: '',
      },
      {
        name: 'National Client Name',
        description: 'The national client name.',
        calculation: 'Whether the client is associated with a national client.',
      },
      {
        name: 'Contact name',
        description: 'The name of the client contact.',
        calculation: '',
      },
      {
        name: 'Email',
        description: 'The email address for the client contact',
        calculation: '',
      },
      {
        name: 'Phone Number',
        description: 'The client contacts phone number.',
        calculation: '',
      },
      {
        name: 'Mobile Number',
        description: 'The client contacts mobile number.',
        calculation: '',
      },
      {
        name: 'Roles',
        description: 'The roles associated with the client contact.',
        calculation: '',
      },
      {
        name: 'Receives Emails',
        description: 'Indicated whether the client contact receives emails.',
        calculation: '',
      },
      {
        name: 'Portal Access',
        description:
          'Indicates whether the client contact can access their client portal.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Subcontractor Details',
    reportType: ReportType.SUBCONTRACTOR_DETAILS,
    endPoint: 'generateReport/subcontractorContactDetails',
    description:
      'The Subcontractor Details report serves as a valuable resource for accessing essential information about subcontractors in your system. It includes contact details such as phone numbers and email addresses, along with their respective signup dates and current status. Users have the flexibility to customize the report by opting to include retired subcontractors, although by default, retired subcontractors are excluded. This feature ensures that you can easily manage and reach out to your active subcontractors while still having the option to access contact information for retired subcontractors when needed. This report simplifies subcontractor administration and facilitates communication and tracking within your subcontractor network.',
    audience: 'The report is intended for internal use.',
    // prettier-ignore
    query: [
        'Subcontractors that belong to the users company and division.',
        'Retired subcontractors will not be included unless \'include Retired\' is checked.',
        'Assets and drivers that are not \'ACTIVE\' will not be included in the count of vehicles and drivers unless \'include Retired\' is checked.'
      ],
    fileName: [
      ReportType.SUBCONTRACTOR_DETAILS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Subcontractor Name',
        description: 'The subcontractors name.',
        calculation: '',
      },
      {
        name: 'Proprietor',
        description: 'The owner of the business.',
        calculation: '',
      },
      {
        name: 'Email',
        description: 'The subcontractors email address.',
        calculation: '',
      },
      {
        name: 'Phone Number',
        description: 'The subcontractors phone number.',
        calculation: '',
      },
      {
        name: 'Mobile Number',
        description: 'The subcontractors mobile number.',
        calculation: '',
      },
      {
        name: 'Affilliation',
        description:
          'The subcontractors affilliation type (Subcontractor, Outside Hire, Equipment Hire, Internal).',
        calculation: '',
      },
      {
        name: 'Trading Terms',
        description: 'The subcontractors trading terms.',
        calculation: '',
      },
      {
        name: 'ABN',
        description: 'The subcontractors Australian business number.',
        calculation: '',
      },
      {
        name: 'ACN',
        description: 'The subcontractors Australian company number.',
        calculation: '',
      },
      {
        name: 'GST Registered',
        description:
          'Indicates whether the subcontractor is registered for GST.',
        calculation: '',
      },
      {
        name: 'Drivers',
        description: 'The number of drivers associated with the subcontractor.',
        calculation: '',
      },
      {
        name: 'Assets',
        description:
          'The number of fleet assets associated with the subcontractor.',
        calculation: '',
      },
      {
        name: 'Devices',
        description: 'The number of devices associated with the subcontractor.',
        calculation: '',
      },
      {
        name: 'Location',
        description: 'The location address of the subcontractor',
        calculation: '',
      },
      {
        name: 'Status',
        description: 'The current status of the subcontractor.',
        calculation: '',
      },
      {
        name: 'Signup Date',
        description: 'The date the subcontractor was entered into the system.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.HEAD_OFFICE_REPORTING,
    name: 'Driver Contact Details',
    reportType: ReportType.DRIVER_CONTACT_DETAILS,
    endPoint: 'generateReport/driverContactDetails',
    description:
      'The Driver Contact Details report serves as a valuable resource for accessing essential information about drivers in your system. It includes contact details such as phone numbers and email addresses, along with their respective signup dates and current status. Users have the flexibility to customize the report by opting to include retired drivers, although by default, retired drivers are excluded. This feature ensures that you can easily manage and reach out to your active drivers while still having the option to access contact information for retired drivers when needed. This report simplifies driver administration and facilitates communication and tracking within your driver network.',
    audience: 'The report is intended for internal use.',
    // prettier-ignore
    query: [
      'Drivers that belong to the users company and division.',
      'Retired drivers will not be included unless \'include Retired\' is checked.'
    ],
    fileName: [
      ReportType.DRIVER_CONTACT_DETAILS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Driver Name',
        description: 'The drivers name.',
        calculation: '',
      },
      {
        name: 'Email',
        description: 'The drivers email address.',
        calculation: '',
      },
      {
        name: 'Phone Number',
        description: 'The drivers phone number.',
        calculation: '',
      },
      {
        name: 'Mobile Number',
        description: 'The drivers mobile number.',
        calculation: '',
      },
      {
        name: 'Location',
        description: 'The drivers location address.',
        calculation: '',
      },
      {
        name: 'Signup Date',
        description: 'The date the driver was entered into the system.',
        calculation: '',
      },
      {
        name: 'Status',
        description: 'The current status of the driver.',
        calculation: '',
      },
    ],
  },
  {
    category: ReportCategory.OPERATIONS_REPORTING,
    name: 'Job Details',
    reportType: ReportType.JOB_DETAILS,
    endPoint: 'generateReport/jobDetails',
    // prettier-ignore
    description: 'The Job Details report provides a comprehensive PDF overview of individual job information, offering access to essential details. It allows you to incorporate images on stops, client charges, subcontractor pay, and event times, making it easy to gain insights and share job details outside of the application. Please be aware that column descriptions are not applicable to this report due to the dynamic nature of the PDF design.',
    audience: 'The report is intended for internal use.',
    query: [
      'The job ID or recurring job ID that is included in the Job ID report parameter.',
    ],
    fileName: [
      ReportType.JOB_DETAILS.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [],
  },
  {
    category: ReportCategory.CLIENT_REPORTING,
    name: 'Distance Rate Pricing',
    reportType: ReportType.DISTANCE_RATE_PRICING,
    endPoint: 'generateReport/distanceRatePricing',
    description:
      'The Distance Rate Pricing Report provides a detailed price list of transport distance rates across multiple services, from an origin suburb to all suburbs within a specified distance. It includes pricing columns for each selected service type, showing the approximate charge (excluding GST) for every destination suburb and distance combination. This report helps clients and staff easily compare service rates across multiple suburbs and service types, supporting both PDF and CSV export formats for flexible viewing and analysis.',
    audience: 'The report is intended to be provided to clients.',
    query: [
      'Applicable Custom Service Rate Card for selected client, or applicable Default Rate Card if no client is selected.',
      'Applicable Service Rate Variations for the given client (if provided).',
      'Finds all suburbs within the specified distance from the origin suburb, and calculates the distance to each suburb.',
    ],
    fileName: [
      ReportType.DISTANCE_RATE_PRICING.toString(),
      'report',
      'company',
      'division',
      'runDate',
    ],
    permissions: [
      UserRoleType.ROLE_ADMIN,
      UserRoleType.ROLE_HEAD_OFFICE,
      UserRoleType.ROLE_TEAM_LEADER,
      UserRoleType.ROLE_BRANCH_MANAGER,
    ],
    columns: [
      {
        name: 'Suburb Name',
        description:
          'The suburbs that were within the specified distance of the origin suburb',
        calculation: '',
      },
      {
        name: 'Columns 1 to N (max 17)',
        description:
          'The price for each service type selected in the report parameters, for the distance to the suburb in that row.',
        calculation: '',
      },
    ],
  },
];
