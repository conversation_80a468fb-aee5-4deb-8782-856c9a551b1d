import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { ReportAccessMethodTypes } from './ReportAccessMethodTypesEnum';
import { ReportFormatType } from './ReportFormatType';
import { ReportSortByEnum } from './ReportSortByEnum';
import { ReportType } from './ReportType';
import { WeightTypeEnum } from './WeightTypeEnum';
import { ServiceTypeAndVariation } from '@/interface-models/Reporting/DistanceRatePricing/ServiceTypeAndVariation';

export interface IGenerateReportRequest {
  startDate: number;
  endDate: number;
  timezone: string;
  reportFormatType: ReportFormatType;
  clientIds: string[];
  statusList: number[];
  includeFuel: boolean | null;
  includeEquipmentHire: boolean | null;
  reportType: ReportType;
  userId: string | null;
  durationInMillisecond: number | null;
  groupByDay: boolean | null;
  sortBy: ReportSortByEnum | null;
  includeRetired: boolean | null;
  weightType: WeightTypeEnum | null;
  queryFieldName: string | null;
  calculateDistanceTravelledFromGps: boolean | null;
  groupByClient: boolean | null;
  invoicedJobs: boolean | null;
  groupsOf: number | null;
  totalNumberOfGroups: number | null;
  isDispatchAddress: boolean | null;
  separateTollCharges: boolean | null;
  excludeReturnToFirstLeg: boolean | null;
  jobId: string | null;
  includeImages: boolean | null;
  includeDriverPay: boolean | null;
  includeClientCharge: boolean | null;
  includeEventTimes: boolean | null;
  accessType: ReportAccessMethodTypes;
  fromSuburb: SuburbAU | null;
  distanceFromOriginInKm: number | null;
  serviceTypes: ServiceTypeAndVariation[] | null;
}

export class GenerateReportRequest implements IGenerateReportRequest {
  constructor(
    public startDate: number = 0,
    public endDate: number = 0,
    public timezone: string = '',
    public reportFormatType: ReportFormatType,
    public clientIds: string[] = [],
    public statusList: number[] = [],
    public includeFuel: boolean | null = false,
    public includeEquipmentHire: boolean | null = true,
    public reportType: ReportType,
    public userId: string | null = null,
    public durationInMillisecond: number | null = null,
    public groupByDay: boolean | null = null,
    public sortBy: ReportSortByEnum | null = null,
    public includeRetired: boolean | null = null,
    public weightType: WeightTypeEnum | null = null,
    public queryFieldName: string | null = null,
    public calculateDistanceTravelledFromGps: boolean | null = null,
    public groupByClient: boolean | null = null,
    public invoicedJobs: boolean | null = null,
    public groupsOf: number | null = null,
    public totalNumberOfGroups: number | null = null,
    public isDispatchAddress: boolean | null = null,
    public separateTollCharges: boolean | null = null,
    public excludeReturnToFirstLeg: boolean | null = null,
    public jobId: string | null = null,
    public includeImages: boolean | null = null,
    public includeDriverPay: boolean | null = null,
    public includeClientCharge: boolean | null = null,
    public includeEventTimes: boolean | null = null,
    public accessType: ReportAccessMethodTypes = ReportAccessMethodTypes.DOWNLOAD,

    /**
     * The suburb where the distances originate from. This will typically be the
     * depot of the client who we are generating the report for.
     */
    public fromSuburb: SuburbAU | null = null,
    /**
     * The distance from the fromSuburb that we will use to find suburbs within
     * range. In kilometres, this is a straight line radial distance (all
     * directions from fromSuburb). We will use the distance to filter our
     * suburbs list to find those that we need to calculate travel distances for.
     *
     * TODO: FEAP-240 do we need to consider a max value?
     */
    public distanceFromOriginInKm: number | null = null,

    /**
     * The service types provided by the frontend. Contains a serviceTypeId and
     * a clientAdjustmentPercentage (see ClientServiceRateVariations). The
     * percentage can be entered on the frontend to provide a custom adjustment
     * for the report. If clientAdjustmentPercentage is NULL, then pro-reports
     * will query the clientServiceRateVariations collection for the clientId to
     * find a rate variation to apply. If none is found, then no percent
     * adjustment is applied.
     *
     * Max 17 entries, as that is all that will fit in the landscape report
     */
    public serviceTypes: ServiceTypeAndVariation[] | null = null, // Max 17 entries
  ) {}
}

export default GenerateReportRequest;
