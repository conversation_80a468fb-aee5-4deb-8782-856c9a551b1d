import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { DivisionOperationDetails } from '@/interface-models/Company/DivisionCustomConfig/Operations/DivisionOperationDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { getMillisDurationAsMinutes } from '../DateTimeHelpers/DateTimeHelpers';
import { sessionManager } from '@/store/session/SessionState';
import { teamLoadUnloadTimesMap } from '@/helpers/BookingHelpers/TeamLoadUnloadTimes';

export const DEFAULT_PUD_LOAD_DURATION_MINS = 30;

/**
 * Updates the PUD (Pickup and Delivery) order while ensuring the first PUD item
 * retains its original time and time definition when necessary.
 * @param {PUDItem[]} pudItems - The list of PUD items.
 * @param {number} originalEpochTime - The original epoch time of the first PUD item.
 * @param {number} originalTimeDefinition - The original time definition of the first PUD item.
 */
export function updatePudOrderWithOriginalTime(
  pudItems: PUDItem[],
  originalEpochTime: number,
  originalTimeDefinition: number,
): void {
  // Restore the original time and time definition for the first PUD item
  const firstPudItem = pudItems[0];
  if (firstPudItem.epochTime !== originalEpochTime) {
    firstPudItem.epochTime = originalEpochTime;
  }
  if (firstPudItem.timeDefinition !== originalTimeDefinition) {
    firstPudItem.timeDefinition = originalTimeDefinition;
  }
  // If the first timeDefinition is ASAP (9), set the rest of the PUD items to
  // estimated (0)
  if (originalTimeDefinition === 9) {
    pudItems.forEach((pud, index) => {
      if (index > 0) {
        pud.timeDefinition = 0;
      }
    });
  }
}

/**
 * Calculates the pickup or dropoff load duration (in minutes) for a booking, based on company, client, division, and service type details.
 *
 * The function determines the duration using the following priority:
 * 1. If the company is 'TEAM', attempts to use a mapped duration based on the service type.
 * 2. If client-specific custom durations are provided, uses those.
 * 3. If division-level custom durations are set, uses those.
 * 4. Falls back to a default duration if none of the above are available.
 *
 * @param isPickup - Indicates whether to calculate the pickup (`true`) or dropoff (`false`) duration.
 * @param clientDetails - The client details object, which may contain custom load durations.
 * @param serviceTypeId - The ID of the service type for the booking.
 * @returns The load duration in minutes for either pickup or dropoff, based on the provided parameters.
 */
export function getPudLoadDurationInMins({
  isPickup,
  clientDetails,
  serviceTypeId,
}: {
  isPickup: boolean;
  clientDetails: ClientDetails | null;
  serviceTypeId: number;
}): number {
  const store = useCompanyDetailsStore();
  const divisionDetails = store.divisionDetails;

  // 1. TEAM company: Use service type mapping if available
  const loadDurationForServiceType =
    getLoadDurationForServiceTypeId(serviceTypeId);
  if (loadDurationForServiceType !== undefined) {
    return isPickup
      ? loadDurationForServiceType.pickupLoadDurationInMins
      : loadDurationForServiceType.dropoffLoadDurationInMins;
  }

  // 2. Client-specific custom durations
  const clientPickupMins = clientDetails?.pickupLoadDuration
    ? getMillisDurationAsMinutes(clientDetails.pickupLoadDuration)
    : null;
  const clientDropoffMins = clientDetails?.dropoffLoadDuration
    ? getMillisDurationAsMinutes(clientDetails.dropoffLoadDuration)
    : null;

  // 3. Division default durations
  const divisionOps = divisionDetails?.customConfig?.operations ?? null;
  const divisionPickupMins = divisionOps?.pickupLoadDuration
    ? getMillisDurationAsMinutes(divisionOps.pickupLoadDuration)
    : null;
  const divisionDropoffMins = divisionOps?.dropoffLoadDuration
    ? getMillisDurationAsMinutes(divisionOps.dropoffLoadDuration)
    : null;

  // 4. Fallback order: client > division > default
  const pickupLoadTimeDuration =
    clientPickupMins || divisionPickupMins || DEFAULT_PUD_LOAD_DURATION_MINS;
  const dropoffLoadTimeDuration =
    clientDropoffMins || divisionDropoffMins || DEFAULT_PUD_LOAD_DURATION_MINS;

  return isPickup ? pickupLoadTimeDuration : dropoffLoadTimeDuration;
}

/**
 * Retrieves the pickup and dropoff load durations (in minutes) for a given
 * service type ID, specifically for companies with the ID 'TEAM'. If the user
 * is not 'TEAM' (temporary) or the service type is not found (no custom values
 * set), returns `undefined`.
 *
 * TODO: This is HARD-CODED temporary solution for TEAM onboarding. Long term solution
 * tracked by: http://jira.godesta.lan/browse/OPS-1617
 *
 * @param serviceTypeId - The unique identifier of the service type.
 * @returns An object containing `pickupLoadDurationInMins` and
 *          `dropoffLoadDurationInMins` if found, or `undefined` if the company
 *          is not 'TEAM' or the service type is not mapped.
 */
export function getLoadDurationForServiceTypeId(
  serviceTypeId: number,
):
  | { pickupLoadDurationInMins: number; dropoffLoadDurationInMins: number }
  | undefined {
  if (sessionManager.getCompanyId() !== 'TEAM') {
    return undefined;
  }
  const serviceTypesMap = useCompanyDetailsStore().serviceTypesMap;
  const foundServiceType = serviceTypesMap.get(serviceTypeId);
  if (foundServiceType?.shortServiceTypeName) {
    const durationInMin = teamLoadUnloadTimesMap.get(
      foundServiceType.shortServiceTypeName,
    );
    if (durationInMin !== undefined) {
      const result = {
        pickupLoadDurationInMins: durationInMin,
        dropoffLoadDurationInMins: durationInMin,
      };
      return result;
    }
  }
}
