// For the provided serviceTypeId, returns the associated ServiceTypes object
// from the store. If no serviceType is found, or the id is invalid, returns
// undefined.

import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';

// TODO: FERP-340 - this is HARD-CODED fix for now, but we need to make this
export const TEAM_DISTANCE_RATE_PRICING_REPORT_SERVICE_TYPES = [
  '1TT',
  '2TT',
  '4TT',
  '6TT',
  '8TT',
  '12T',
  '14T',
  '6CT',
  'LCT',
  'HLT',
  'SCT',
  '1FT',
  '2FT',
  '3FT',
  'SFT',
  'STT',
  'BDT',
];

/**
 * Returns the associated ServiceTypes object from the store for the provided
 * serviceTypeId. If no serviceType is found, or the id is invalid, returns
 * undefined.
 * @param serviceTypeId - The id of the service type to find
 * @returns - The associated ServiceTypes object, or undefined
 */
export function getServiceTypeById(
  serviceTypeId: number | null | undefined,
): ServiceTypes | undefined {
  const types = useCompanyDetailsStore().serviceTypesMap;
  if (serviceTypeId === null || serviceTypeId === undefined) {
    return;
  }
  // Try to find ServiceTypes object
  return types.get(serviceTypeId);
}
