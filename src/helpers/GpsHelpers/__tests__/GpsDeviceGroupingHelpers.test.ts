import {
  groupGpsDataByDevice,
  getDefaultDeviceId,
  createDeviceSelectionOptions,
  getGpsPositionsForDevice,
  createDeviceDisplayName,
  getUniqueDeviceIds,
} from '../GpsDeviceGroupingHelpers';
import GpsPosition from '@/interface-models/Generic/Position/GpsPosition';

describe('GpsDeviceGroupingHelpers', () => {
  const mockGpsData: GpsPosition[] = [
    {
      deviceId: 'device1driver001',
      driverId: '001',
      fleetAssetId: 'asset001',
      latitude: -37.8136,
      longitude: 144.9631,
      timestamp: '2023-01-01T10:00:00Z',
    } as GpsPosition,
    {
      deviceId: 'device1driver001',
      driverId: '001',
      fleetAssetId: 'asset001',
      latitude: -37.8140,
      longitude: 144.9635,
      timestamp: '2023-01-01T10:01:00Z',
    } as GpsPosition,
    {
      deviceId: 'device2driver002',
      driverId: '002',
      fleetAssetId: 'asset002',
      latitude: -37.8150,
      longitude: 144.9640,
      timestamp: '2023-01-01T10:00:00Z',
    } as GpsPosition,
    {
      deviceId: 'device3driver003',
      driverId: '003',
      fleetAssetId: 'asset003',
      latitude: -37.8160,
      longitude: 144.9650,
      timestamp: '2023-01-01T10:00:00Z',
    } as GpsPosition,
    {
      deviceId: 'device3driver003',
      driverId: '003',
      fleetAssetId: 'asset003',
      latitude: -37.8165,
      longitude: 144.9655,
      timestamp: '2023-01-01T10:01:00Z',
    } as GpsPosition,
    {
      deviceId: 'device3driver003',
      driverId: '003',
      fleetAssetId: 'asset003',
      latitude: -37.8170,
      longitude: 144.9660,
      timestamp: '2023-01-01T10:02:00Z',
    } as GpsPosition,
  ];

  describe('groupGpsDataByDevice', () => {
    it('should group GPS data by deviceId correctly', () => {
      const result = groupGpsDataByDevice(mockGpsData);
      
      expect(result.size).toBe(3);
      expect(result.has('device1driver001')).toBe(true);
      expect(result.has('device2driver002')).toBe(true);
      expect(result.has('device3driver003')).toBe(true);
      
      expect(result.get('device1driver001')?.dataCount).toBe(2);
      expect(result.get('device2driver002')?.dataCount).toBe(1);
      expect(result.get('device3driver003')?.dataCount).toBe(3);
    });

    it('should handle empty GPS data', () => {
      const result = groupGpsDataByDevice([]);
      expect(result.size).toBe(0);
    });

    it('should skip positions without deviceId', () => {
      const dataWithMissingDeviceId = [
        ...mockGpsData,
        {
          deviceId: '',
          driverId: '004',
          fleetAssetId: 'asset004',
          latitude: -37.8180,
          longitude: 144.9670,
          timestamp: '2023-01-01T10:00:00Z',
        } as GpsPosition,
      ];
      
      const result = groupGpsDataByDevice(dataWithMissingDeviceId);
      expect(result.size).toBe(3); // Should still be 3, not 4
    });
  });

  describe('getDefaultDeviceId', () => {
    it('should return the deviceId with the most data points', () => {
      const deviceGroups = groupGpsDataByDevice(mockGpsData);
      const defaultDeviceId = getDefaultDeviceId(deviceGroups);
      
      expect(defaultDeviceId).toBe('device3driver003'); // Has 3 data points
    });

    it('should return null for empty device groups', () => {
      const emptyGroups = new Map();
      const defaultDeviceId = getDefaultDeviceId(emptyGroups);
      
      expect(defaultDeviceId).toBeNull();
    });
  });

  describe('createDeviceSelectionOptions', () => {
    it('should create selection options sorted by data count', () => {
      const deviceGroups = groupGpsDataByDevice(mockGpsData);
      const options = createDeviceSelectionOptions(deviceGroups);
      
      expect(options).toHaveLength(3);
      expect(options[0].value).toBe('device3driver003'); // Most data points first
      expect(options[0].dataCount).toBe(3);
      expect(options[1].value).toBe('device1driver001'); // Second most
      expect(options[1].dataCount).toBe(2);
      expect(options[2].value).toBe('device2driver002'); // Least data points
      expect(options[2].dataCount).toBe(1);
    });
  });

  describe('getGpsPositionsForDevice', () => {
    it('should return positions for the specified device', () => {
      const deviceGroups = groupGpsDataByDevice(mockGpsData);
      const positions = getGpsPositionsForDevice(deviceGroups, 'device1driver001');
      
      expect(positions).toHaveLength(2);
      expect(positions[0].deviceId).toBe('device1driver001');
      expect(positions[1].deviceId).toBe('device1driver001');
    });

    it('should return empty array for non-existent device', () => {
      const deviceGroups = groupGpsDataByDevice(mockGpsData);
      const positions = getGpsPositionsForDevice(deviceGroups, 'nonexistent');
      
      expect(positions).toHaveLength(0);
    });

    it('should return empty array for null deviceId', () => {
      const deviceGroups = groupGpsDataByDevice(mockGpsData);
      const positions = getGpsPositionsForDevice(deviceGroups, null);
      
      expect(positions).toHaveLength(0);
    });
  });

  describe('createDeviceDisplayName', () => {
    it('should create display name with driver info when deviceId contains "driver"', () => {
      const displayName = createDeviceDisplayName('device1driver001', '001', 'asset001');
      expect(displayName).toBe('Driver 001 (Asset asset001)');
    });

    it('should create fallback display name when deviceId does not contain "driver"', () => {
      const displayName = createDeviceDisplayName('somedeviceid', '001', 'asset001');
      expect(displayName).toBe('Device somedeviceid (Asset asset001)');
    });

    it('should truncate long device IDs', () => {
      const longDeviceId = 'verylongdeviceidthatexceedstwentycharacters';
      const displayName = createDeviceDisplayName(longDeviceId, '001', 'asset001');
      expect(displayName).toBe('Device verylongdeviceidtha... (Asset asset001)');
    });
  });

  describe('getUniqueDeviceIds', () => {
    it('should return unique device IDs', () => {
      const uniqueIds = getUniqueDeviceIds(mockGpsData);
      
      expect(uniqueIds).toHaveLength(3);
      expect(uniqueIds).toContain('device1driver001');
      expect(uniqueIds).toContain('device2driver002');
      expect(uniqueIds).toContain('device3driver003');
    });

    it('should handle empty GPS data', () => {
      const uniqueIds = getUniqueDeviceIds([]);
      expect(uniqueIds).toHaveLength(0);
    });

    it('should skip positions without deviceId', () => {
      const dataWithMissingDeviceId = [
        ...mockGpsData,
        {
          deviceId: '',
          driverId: '004',
          fleetAssetId: 'asset004',
          latitude: -37.8180,
          longitude: 144.9670,
          timestamp: '2023-01-01T10:00:00Z',
        } as GpsPosition,
      ];
      
      const uniqueIds = getUniqueDeviceIds(dataWithMissingDeviceId);
      expect(uniqueIds).toHaveLength(3); // Should still be 3, not 4
    });
  });
});
