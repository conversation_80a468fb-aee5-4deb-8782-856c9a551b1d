import GpsPosition, {
  DeviceSelectionOption,
  GpsDeviceGroup,
} from '@/interface-models/Generic/Position/GpsPosition';

/**
 * Groups GPS position data by deviceId
 * @param gpsData Array of GPS position data to group
 * @returns Map of deviceId to GpsDeviceGroup
 */
export function groupGpsDataByDevice(
  gpsData: GpsPosition[],
): Map<string, GpsDeviceGroup> {
  const deviceGroups = new Map<string, GpsDeviceGroup>();

  if (!gpsData || gpsData.length === 0) {
    return deviceGroups;
  }

  gpsData.forEach((position) => {
    const { deviceId, driverId, fleetAssetId } = position;

    if (!deviceId) {
      return; // Skip positions without deviceId
    }

    if (deviceGroups.has(deviceId)) {
      const group = deviceGroups.get(deviceId)!;
      group.positions.push(position);
      group.dataCount = group.positions.length;
    } else {
      // Create display name from deviceId and driverId
      const displayName = createDeviceDisplayName(
        deviceId,
        driverId,
        fleetAssetId,
      );

      const newGroup: GpsDeviceGroup = {
        deviceId,
        driverId,
        fleetAssetId,
        dataCount: 1,
        positions: [position],
        displayName,
      };

      deviceGroups.set(deviceId, newGroup);
    }
  });

  return deviceGroups;
}

/**
 * Creates a user-friendly display name for a device
 * @param deviceId The device identifier
 * @param driverId The driver identifier
 * @param fleetAssetId The fleet asset identifier
 * @returns Formatted display name
 */
export function createDeviceDisplayName(
  deviceId: string,
  driverId: string,
  fleetAssetId: string,
): string {
  // Extract meaningful parts from deviceId if it contains driver info
  if (deviceId.includes('driver') && driverId) {
    // Extract the driver number from the deviceId to make devices distinguishable
    const driverMatch = deviceId.match(/driver(\d+)/);
    const deviceDriverNumber = driverMatch ? driverMatch[1] : driverId;

    // Include a portion of the deviceId to make devices with same driver distinguishable
    const devicePrefix = deviceId.substring(0, 8);
    return `Driver ${deviceDriverNumber} (${devicePrefix}... - Asset ${fleetAssetId})`;
  }

  // Fallback to deviceId with asset info
  const shortDeviceId =
    deviceId.length > 20 ? `${deviceId.substring(0, 20)}...` : deviceId;
  return `Device ${shortDeviceId} (Asset ${fleetAssetId})`;
}

/**
 * Determines the default device group (the one with the most data points)
 * @param deviceGroups Map of device groups
 * @returns The deviceId of the group with the most data, or null if no groups
 */
export function getDefaultDeviceId(
  deviceGroups: Map<string, GpsDeviceGroup>,
): string | null {
  if (deviceGroups.size === 0) {
    return null;
  }

  let maxDataCount = 0;
  let defaultDeviceId: string | null = null;

  deviceGroups.forEach((group, deviceId) => {
    if (group.dataCount > maxDataCount) {
      maxDataCount = group.dataCount;
      defaultDeviceId = deviceId;
    }
  });

  return defaultDeviceId;
}

/**
 * Creates device selection options for dropdown component
 * @param deviceGroups Map of device groups
 * @returns Array of selection options sorted by data count (descending)
 */
export function createDeviceSelectionOptions(
  deviceGroups: Map<string, GpsDeviceGroup>,
): DeviceSelectionOption[] {
  const options: DeviceSelectionOption[] = [];

  deviceGroups.forEach((group, deviceId) => {
    options.push({
      value: deviceId,
      text: `${group.displayName} (${group.dataCount} points)`,
      dataCount: group.dataCount,
    });
  });

  // Sort by data count in descending order (most data first)
  return options.sort((a, b) => b.dataCount - a.dataCount);
}

/**
 * Gets GPS positions for a specific device
 * @param deviceGroups Map of device groups
 * @param deviceId The device identifier to get positions for
 * @returns Array of GPS positions for the specified device, or empty array if not found
 */
export function getGpsPositionsForDevice(
  deviceGroups: Map<string, GpsDeviceGroup>,
  deviceId: string | null,
): GpsPosition[] {
  if (!deviceId || !deviceGroups.has(deviceId)) {
    return [];
  }

  return deviceGroups.get(deviceId)!.positions;
}

/**
 * Gets all unique device IDs from GPS data
 * @param gpsData Array of GPS position data
 * @returns Array of unique device IDs
 */
export function getUniqueDeviceIds(gpsData: GpsPosition[]): string[] {
  const deviceIds = new Set<string>();

  gpsData.forEach((position) => {
    if (position.deviceId) {
      deviceIds.add(position.deviceId);
    }
  });

  return Array.from(deviceIds);
}
