<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :ids.sync="excludedClients"
            :multiple="true"
            :rules="[]"
            :hint="'Clients you wish to exclude from the daily jobs report.'"
            :placeholder="'Excluded Clients'"
          />
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartOfDayEpoch"
            :hideIcon="true"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startOfDayEpoch"
          >
          </DatePickerBasic>
        </v-flex>

        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndOfDayEpoch"
            :hideIcon="true"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endOfDayEpoch"
          >
          </DatePickerBasic>
        </v-flex>

        <v-flex md12>
          <v-checkbox
            v-model="separateTollCharges"
            :label="'Print Toll Charges Individually'"
            hint="Whether the sum of all toll charges will be printed on a single row or on separate individual rows."
            persistent-hint
          >
          </v-checkbox>
        </v-flex>
      </v-layout>
    </v-form>

    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center class="pr-2">
            <ConfirmationDialog
              buttonText="Send Emails"
              message="Please confirm that you wish to email clients their daily jobs report."
              :isSmallButton="true"
              title="Confirm Bulk Client Emails"
              @confirm="generateReport"
              :buttonDisabled="!isAuthorised()"
              :isOutlineButton="false"
              :buttonColor="'orange'"
              :confirmationButtonText="'Confirm'"
              :isBlockButton="true"
              :isCheckbox="false"
              :isDepressedButton="true"
            />
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { Ref, ref } from 'vue';

const excludedClients: Ref<string[]> = ref([]);
const separateTollCharges: Ref<boolean> = ref(false);

const startOfDayEpoch: Ref<number> = ref(returnStartOfDayFromEpoch());
const endOfDayEpoch: Ref<number> = ref(returnEndOfDayFromEpoch());
const form = ref<any>(null);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

// Handle response from date picker component
const setStartOfDayEpoch = (epoch: number | null): void => {
  if (epoch !== null) {
    startOfDayEpoch.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndOfDayEpoch = (epoch: number | null): void => {
  if (epoch !== null) {
    endOfDayEpoch.value = returnEndOfDayFromEpoch(epoch);
  }
};

function generateReport() {
  if (!form.value.validate()) {
    showNotification('Please ensure all required fields are completed.', {
      type: HealthLevel.ERROR,
      title: 'Email Daily Jobs Report',
    });
    return;
  }

  const requestPayload: GenerateReportRequest = {
    clientIds: excludedClients.value,
    startDate: startOfDayEpoch.value,
    endDate: endOfDayEpoch.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: ReportFormatType.PDF,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.DAILY_JOBS,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: false,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: null,
    separateTollCharges: separateTollCharges.value,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: ReportAccessMethodTypes.EMAIL,
    fromSuburb: null,
    distanceFromOriginInKm: null,
    serviceTypes: null,
  };

  emit('update:isLoading', false);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
