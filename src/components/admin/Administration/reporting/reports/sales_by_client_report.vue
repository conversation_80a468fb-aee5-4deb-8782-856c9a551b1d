<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
            :selectableDates="selectableDates"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pr">
          <v-text-field
            type="number"
            min="1"
            label="Total number of weeks to be grouped"
            v-model.number="groupsOf"
            :rules="[
              validate.required,
              (v) => v > 0 || 'minimum allowable value is 1',
            ]"
            hint="number of weeks that should be grouped into one column."
            persistent-hint
            solo
            flat
            class="v-solo-custom"
          />
        </v-flex>

        <v-flex md6 class="pl">
          <v-text-field
            type="number"
            class="v-solo-custom"
            min="1"
            :rules="[
              validate.required,
              (v) => v > 0 || 'minimum allowable value is 1',
              (v) => v < 21 || 'maximum allowable value is 20',
            ]"
            max="20"
            label="Total Number of Groups"
            v-model.number="totalNumberOfGroups"
            hint="Total number of columns the report should include."
            persistent-hint
            solo
            flat
          />
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { computed, Ref, ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const groupsOf: Ref<number> = ref(1);
const totalNumberOfGroups: Ref<number> = ref(12);
const form: Ref<any> = ref(null);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate() || !startDate.value) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  const requestPayload: GenerateReportRequest = {
    clientIds: [],
    startDate: startDate.value,
    endDate: startDate.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: ReportFormatType.CSV,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.SALES_BY_CLIENT,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: null,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: groupsOf.value,
    totalNumberOfGroups: totalNumberOfGroups.value,
    isDispatchAddress: null,
    separateTollCharges: null,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
    fromSuburb: null,
    distanceFromOriginInKm: null,
    serviceTypes: null,
  };
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

// return a list of selectable dates for our date picker component prop
// in year-month-day string format. The dates returns the the only dates the user can select
const selectableDates = computed(() => {
  const dates: string[] = [];
  const currentYear = moment().year();
  const startingYear = currentYear - 10;
  const endingYear = currentYear + 10;
  for (let year = startingYear; year <= endingYear; year++) {
    for (let month = 1; month <= 12; month++) {
      const monthOfYear = month < 10 ? '0' + month : month.toString();
      const daysInMonth = moment(
        `${year}-${monthOfYear}`,
        'YYYY-MM',
      ).daysInMonth();
      for (let day = 1; day <= daysInMonth; day++) {
        const dayOfMonth = day < 10 ? '0' + day : day.toString();
        const date = `${year}-${monthOfYear}-${dayOfMonth}`;
        const dayOfWeek = moment(date, 'YYYY-MM-DD').isoWeekday();
        if (dayOfWeek === 1) {
          dates.push(date);
        }
      }
    }
  }
  return dates;
});

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>
