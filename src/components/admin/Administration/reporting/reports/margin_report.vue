<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <v-select
            v-model="selectedFormatType"
            :items="
              Object.keys(ReportFormatType).map((e) => ReportFormatType[e])
            "
            label="Format Type"
            hint="The report file type."
            persistent-hint
            color="primary"
            solo
            flat
            class="v-solo-custom"
          >
          </v-select>
        </v-flex>
        <v-flex md6 class="pr">
          <DatePickerBasic
            @setEpoch="setStartDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'Start Date'"
            :soloInput="true"
            :epochTime="startDate"
          >
          </DatePickerBasic>
        </v-flex>
        <v-flex md6 class="pl">
          <DatePickerBasic
            @setEpoch="setEndDate"
            :hideIcon="true"
            :isRequired="true"
            :validate="validate"
            :labelName="'End Date'"
            :soloInput="true"
            :epochTime="endDate"
          >
          </DatePickerBasic>
        </v-flex>
      </v-layout>
    </v-form>

    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import {
  returnEndOfDayFromEpoch,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { ref, Ref } from 'vue';

const validate = validationRules;

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());
const endDate: Ref<number> = ref(returnEndOfDayFromEpoch());
const selectedFormatType: Ref<ReportFormatType> = ref(ReportFormatType.PDF);
const form: Ref<any> = ref(null);

const company = useCompanyDetailsStore().companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;

// Handle response from date picker component
const setStartDate = (epoch: number | null): void => {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
};
const setEndDate = (epoch: number | null): void => {
  if (epoch !== null) {
    endDate.value = returnEndOfDayFromEpoch(epoch);
  }
};

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    return;
  }

  const requestPayload: GenerateReportRequest = {
    clientIds: [],
    startDate: startDate.value,
    endDate: endDate.value,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: selectedFormatType.value,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.MARGIN,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: false,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: null,
    separateTollCharges: null,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
    fromSuburb: null,
    distanceFromOriginInKm: null,
    serviceTypes: null,
  };

  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}
</script>

<style scoped lang="scss">
.daily-margin-report {
  padding: 0;
  z-index: -1 !important;

  table {
    border-spacing: 1px;
    border-collapse: collapse;
    min-width: 100%;
  }

  .margin-report__content {
    display: none;
    .margin-report__header {
      border-bottom: 14px solid transparent;
      .margin-report__title {
        // display: inline;
        font-size: $font-size-20;
        font-weight: 700;
        text-transform: uppercase;
        margin-bottom: 2px;
      }
      .margin-report__headervalue {
        margin: 0px;
        font-size: $font-size-13;
      }
    }

    .margin-report__body {
      // page-break-inside: avoid;
      $horizontal-cell-padding: 6px;
      border-top: 10px solid transparent;

      .margin-report__titlerow {
        font-size: $font-size-13;
        font-weight: 700;

        border-bottom: 2px solid black;
        .margin-report__titlecell {
          padding: 2px $horizontal-cell-padding 2px $horizontal-cell-padding;
        }
      }
      .margin-report__totalsrow {
        .margin-report__cell {
          padding: 4px $horizontal-cell-padding;
        }
      }
      .margin-report__cell {
        font-size: $font-size-11;
        padding: 1px $horizontal-cell-padding;
        text-align: left;

        &.header-cell {
          // padding: 8px 6px;
          font-weight: 600;
          padding: 2px $horizontal-cell-padding 1px $horizontal-cell-padding;
        }

        &.right-align {
          text-align: right;
        }
        &.center-align {
          text-align: center;
        }
        &.add-divider {
          border-left: 1px solid rgb(0, 0, 0);
        }
      }
    }
  }
}
</style>
