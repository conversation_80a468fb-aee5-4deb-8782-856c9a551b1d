<template>
  <section>
    <v-form ref="form">
      <v-layout wrap>
        <v-flex md12>
          <v-layout align-center justify-space-between>
            <h6 class="form-field-required-marker">Report Format:</h6>
          </v-layout>
          <v-select
            v-model="selectedFormatType"
            :items="
              Object.keys(ReportFormatType).map((e) => ReportFormatType[e])
            "
            label="Format Type"
            hint="The report file type."
            persistent-hint
            color="primary"
            solo
            flat
            class="v-solo-custom"
          >
          </v-select>
        </v-flex>
        <v-flex md6 class="pr pt-3">
          <v-layout align-center justify-space-between>
            <h6 class="form-field-required-marker">Report Date:</h6>
          </v-layout>
          <v-layout>
            <DatePickerBasic
              @setEpoch="setStartDate"
              :hideIcon="true"
              :isRequired="true"
              :validate="validationRules"
              :labelName="'Choose a date to list rates for'"
              :soloInput="true"
              :epochTime="startDate"
            >
            </DatePickerBasic>
          </v-layout>
        </v-flex>
        <v-flex md6 class="pl pt-3">
          <v-layout align-center justify-space-between>
            <h6 class="pr-3">Client (optional):</h6>
          </v-layout>
          <SelectEntity
            :entityTypes="[EntityType.CLIENT]"
            :id.sync="clientId"
            hint="Select a specific client to run the report for, or leave blank for default rates. All applicable Service Rate Variations will be included in estimates."
          />
        </v-flex>

        <v-flex md6 class="pr pt-3">
          <v-layout align-center justify-space-between>
            <h6 class="form-field-required-marker">From Suburb:</h6>
          </v-layout>
          <AddressSuburbSearch
            :address="address"
            :formDisabled="!isAuthorised()"
            :setFocus="false"
            :searchAddress="false"
            :enableSuburbSelect="true"
            :solo="true"
            :hint="'Select an origin suburb for distance calculation'"
            @suburbSelected="onSuburbSelected"
          />
        </v-flex>
        <v-flex md6 class="pl pt-3">
          <v-layout align-center justify-space-between>
            <h6 class="form-field-required-marker">
              Include Suburbs in Radius (km):
            </h6>
          </v-layout>
          <v-text-field
            v-model="distanceFromOriginInKm"
            label="Distance From Origin (km)"
            hint="Enter distance from origin in kilometers"
            persistent-hint
            :rules="[
              validationRules.required,
              validationRules.number,
              validateDistanceFromOrigin,
            ]"
            class="v-solo-custom"
            solo
            flat
            min="1"
            max="500"
            :disabled="!isAuthorised()"
            suffix="km"
          />
        </v-flex>
        <v-flex md12 pt-3>
          <v-layout align-center justify-space-between>
            <h6 class="form-field-required-marker">
              Select Services to Include:
            </h6>
          </v-layout>
          <v-layout>
            <v-flex md6 class="pr pt-3">
              <v-select
                v-model="serviceTypeModeController"
                :items="serviceTypeModeOptions"
                item-text="name"
                item-value="id"
                label="Service Type"
                hint="The report service type."
                persistent-hint
                color="primary"
                solo
                flat
                :disabled="serviceTypeModeOptions.length < 2 || !isAuthorised()"
                class="v-solo-custom"
              >
              </v-select>
            </v-flex>
            <v-flex md6 class="pl pt-3">
              <v-autocomplete
                v-model="serviceTypeIdsController"
                :items="serviceTypesList"
                label="Select Services"
                hint="Select services to include in the report"
                item-text="optionSelectName"
                item-value="serviceTypeId"
                class="v-solo-custom"
                multiple
                small-chips
                clearable
                deletable-chips
                persistent-hint
                solo
                flat
                dense
                :disabled="!isAuthorised()"
                :rules="[serviceTypeLengthValidator(1, 17)]"
              />
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-form>
    <div class="action-bar">
      <v-layout>
        <v-flex md12>
          <v-layout justify-end align-center>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.DOWNLOAD,
                )
              "
              depressed
              class="download"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.DOWNLOAD)"
            >
              <v-icon class="pr-2" size="20">downloading</v-icon>
              Download Report
            </v-btn>
            <v-btn
              v-if="
                reportsSettings?.allowedAccessMethods?.includes(
                  ReportAccessMethodTypes.EMAIL,
                )
              "
              depressed
              class="email"
              small
              :loading="isLoading"
              :disabled="isLoading || !isAuthorised()"
              @click="generateReport(ReportAccessMethodTypes.EMAIL)"
            >
              <v-icon class="pr-2" size="20">forward_to_inbox</v-icon>
              Email Report
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </div>
  </section>
</template>

<script setup lang="ts">
enum ServiceTypeSelectionMode {
  PRESET,
  CUSTOM,
}

import AddressSuburbSearch from '@/components/common/addressing/address-search-au/address_suburb_search.vue';
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import SelectEntity from '@/components/common/inputs/select_entity/index.vue';
import { returnStartOfDayFromEpoch } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { TEAM_DISTANCE_RATE_PRICING_REPORT_SERVICE_TYPES } from '@/helpers/StaticDataHelpers/ServiceTypeHelpers';
import { hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import SuburbAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/SuburbAU';
import { EntityType } from '@/interface-models/Generic/EntityType';
import { SelectOption } from '@/interface-models/Generic/ShortLongName/SelectOption';
import { ServiceTypeAndVariation } from '@/interface-models/Reporting/DistanceRatePricing/ServiceTypeAndVariation';
import GenerateReportRequest from '@/interface-models/Reporting/GenerateReportRequest';
import { ReportAccessMethodTypes } from '@/interface-models/Reporting/ReportAccessMethodTypesEnum';
import { ReportFormatType } from '@/interface-models/Reporting/ReportFormatType';
import { ReportType } from '@/interface-models/Reporting/ReportType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import { computed, onBeforeMount, ref, Ref, WritableComputedRef } from 'vue';

const props = withDefaults(
  defineProps<{
    isLoading?: boolean;
  }>(),
  {
    isLoading: false,
  },
);

const emit = defineEmits<{
  (event: 'update:isLoading', value: boolean): void;
  (event: 'generateReport', payload: GenerateReportRequest): void;
}>();

const form = ref<any>(null);

const includeRetired: Ref<boolean> = ref(false);

const companyDetailsStore = useCompanyDetailsStore();
const clientId: Ref<string> = ref('');
const company = companyDetailsStore.companyDetails;
const reportsSettings = company?.divisions?.[0]?.customConfig?.reports;
const serviceTypesList = companyDetailsStore.getServiceTypesList;
const serviceTypeModeOptions: SelectOption<ServiceTypeSelectionMode>[] = [
  { id: ServiceTypeSelectionMode.PRESET, name: 'Preset' },
  { id: ServiceTypeSelectionMode.CUSTOM, name: 'Custom Selection' },
];

const selectedFormatType: Ref<ReportFormatType> = ref(ReportFormatType.PDF);
const selectedServiceTypeMode: Ref<ServiceTypeSelectionMode> = ref(
  ServiceTypeSelectionMode.PRESET,
);
const serviceTypeAndVariation: Ref<ServiceTypeAndVariation[]> = ref([]);
const distanceFromOriginInKm: Ref<number | null> = ref(100.0);
const address: Ref<AddressAU> = ref(new AddressAU());
const startDate: Ref<number> = ref(returnStartOfDayFromEpoch());

// Handle response from date picker component
function setStartDate(epoch: number | null): void {
  if (epoch !== null) {
    startDate.value = returnStartOfDayFromEpoch(epoch);
  }
}

/**
 * Used in the template as the v-model for the service type mode (either CUSTOM
 * or PRESET). Currently preset is hard-coded for TEAM company, and so only
 * CUSTOM is available for other companies. This may be extended or removed in
 * the future.
 */
const serviceTypeModeController: WritableComputedRef<ServiceTypeSelectionMode> =
  computed({
    get(): ServiceTypeSelectionMode {
      return selectedServiceTypeMode.value;
    },
    set(value: ServiceTypeSelectionMode): void {
      if (value === ServiceTypeSelectionMode.PRESET) {
        const serviceTypeIds =
          returnPresetServiceTypesForCompany(sessionManager.getCompanyId()) ??
          [];
        serviceTypeAndVariation.value = serviceTypeIds.map((id) => ({
          serviceTypeId: id,
          variationId: null,
        }));
      } else {
        // Clear selected service types when switching modes
        serviceTypeAndVariation.value = [];
      }
      selectedServiceTypeMode.value = value;
    },
  });

/**
 * Used in the template as the v-model for the service type IDs selected in
 * the multi-select. This is a computed property that gets and sets the
 * serviceTypeAndVariation array based on the selected service type IDs.
 */
const serviceTypeIdsController: WritableComputedRef<number[]> = computed({
  get(): number[] {
    return serviceTypeAndVariation.value.map((item) => item.serviceTypeId);
  },
  set(value: number[]): void {
    // Set serviceTypeAndVariation based on selected serviceTypeIds
    serviceTypeAndVariation.value = value.map((id) => ({
      serviceTypeId: id,
      variationId: null,
    }));
  },
});

/**
 * Returns a list of preset service type IDs based on the provided company
 * identifier.
 *
 * TODO: FERP-340 - this is HARD-CODED fix for now, but we need a long-term
 * solution in the future
 */
function returnPresetServiceTypesForCompany(company: string): number[] | null {
  switch (company) {
    case 'TEAM':
      return TEAM_DISTANCE_RATE_PRICING_REPORT_SERVICE_TYPES.map(
        (s) =>
          serviceTypesList.find((st) => st.shortServiceTypeName === s)
            ?.serviceTypeId,
      ).filter((id): id is number => id !== undefined);
    default:
      return null;
  }
}

/**
 * Validates that the provided value is an array with a length between the specified minimum and maximum.
 *
 * @param min - The minimum number of service types required.
 * @param max - The maximum number of service types allowed.
 * @returns  A validator function that returns true if the value is valid,
 * or an error message if the value is invalid.
 */
function serviceTypeLengthValidator(min: number, max: number) {
  return (value: any) =>
    (Array.isArray(value) && value.length >= min && value.length <= max) ||
    `Please select between ${min} and ${max} service types.`;
}

/**
 * Validates that the value is a number greater than 0 and less than 500.
 * @param value - The value to validate.
 * @returns true if valid, or an error message string if invalid.
 */
function validateDistanceFromOrigin(value: number | null): true | string {
  if (typeof value !== 'number' || isNaN(value)) {
    return 'Please enter a valid number.';
  }
  if (value <= 0) {
    return 'Distance must be greater than 0 km.';
  }
  if (value >= 500) {
    return 'Distance must be less than 500 km.';
  }
  return true;
}

// Handle suburb selection from the AddressSuburbSearch component
function onSuburbSelected(suburb: SuburbAU): void {
  if (suburb) {
    const addressFromSuburb = new AddressAU();
    addressFromSuburb.fromSuburb(suburb);
    address.value = addressFromSuburb;
  } else {
    address.value = new AddressAU();
  }
}

/**
 * Generates the report based on the selected parameters and emits the
 * 'generateReport' event with the request payload.
 * @param runReportType - The method to access the report (e.g., DOWNLOAD or EMAIL).
 */
function generateReport(runReportType: ReportAccessMethodTypes) {
  if (!form.value.validate()) {
    showNotification(FORM_VALIDATION_FAILED_MESSAGE);
  }

  const suburb = address.value.toSuburb();
  if (
    !suburb.name ||
    !suburb.postcode ||
    !suburb.state ||
    !distanceFromOriginInKm.value ||
    distanceFromOriginInKm.value <= 0
  ) {
    showNotification(
      `Please ensure you've selected a valid origin suburb and distance.`,
    );
    return;
  }

  if (!serviceTypeAndVariation.value.length) {
    showNotification(
      `Please select at least one service type, up to a maximum of 17.`,
    );
    return;
  }

  const requestPayload: GenerateReportRequest = {
    clientIds: clientId.value ? [clientId.value] : [],
    startDate: startDate.value,
    endDate: 0,
    timezone: useCompanyDetailsStore().userLocale,
    reportFormatType: selectedFormatType.value,
    statusList: [],
    includeFuel: null,
    includeEquipmentHire: null,
    reportType: ReportType.DISTANCE_RATE_PRICING,
    userId: null,
    durationInMillisecond: null,
    groupByDay: null,
    sortBy: null,
    includeRetired: includeRetired.value,
    weightType: null,
    queryFieldName: null,
    calculateDistanceTravelledFromGps: null,
    groupByClient: null,
    invoicedJobs: null,
    groupsOf: null,
    totalNumberOfGroups: null,
    isDispatchAddress: null,
    separateTollCharges: null,
    excludeReturnToFirstLeg: null,
    jobId: null,
    includeImages: null,
    includeDriverPay: null,
    includeClientCharge: null,
    includeEventTimes: null,
    accessType: runReportType,
    fromSuburb: address.value.toSuburb(),
    distanceFromOriginInKm: distanceFromOriginInKm.value,
    serviceTypes: serviceTypeAndVariation.value,
  };
  emit('update:isLoading', true);
  emit('generateReport', requestPayload);
}

function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeOrTeamLeaderOrBranchManagerRole();
}

onBeforeMount(() => {
  const presetServiceTypeIds = returnPresetServiceTypesForCompany(
    sessionManager.getCompanyId(),
  );

  if (presetServiceTypeIds === null) {
    // If no preset service types are defined for the company, remove the PRESET
    // option and default to CUSTOM
    serviceTypeModeOptions.splice(
      serviceTypeModeOptions.findIndex(
        (option) => option.id === ServiceTypeSelectionMode.PRESET,
      ),
      1,
    );
    selectedServiceTypeMode.value = ServiceTypeSelectionMode.CUSTOM;
  } else {
    // Default to PRESET mode and set the preset service types
    selectedServiceTypeMode.value = ServiceTypeSelectionMode.PRESET;
    serviceTypeAndVariation.value = presetServiceTypeIds.map((id) => ({
      serviceTypeId: id,
      variationId: null,
    }));
  }
});
</script>
