.map-route {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 22px;
  border: 1px solid $border-color;

  $container-bg: $app-dark-primary-400;
  $container-border: var(--background-color-650);
  $container-label-color: #ffffff;

  .map-route-legend {
    position: absolute;
    top: 8px;
    right: 44px;
    background-color: rgb(255, 255, 255);
    border-radius: 8px;
    margin: 2px 4px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.404);
    z-index: 4;
    padding: 8px;
    $font-awesome-family: $font-awesome-family;
    color: black;
    .legend-subtitle {
      font-size: $font-size-11;
      font-weight: 600;
      font-family: $sub-font-family;
      text-transform: uppercase;
      margin: 2px 8px;
    }
    .legend-item {
      .legend-item-label {
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: $font-size-12;
        padding: 1px 8px;

        &::before {
          content: '\f601';
          font-family: $font-awesome-family;
          text-decoration: none;
          font-weight: 400;
          margin-right: 4px;
          font-size: $font-size-12;
          display: inline-block;
        }

        &.start-type {
          &::before {
            color: green;
          }
        }
        &.end-type {
          &::before {
            color: #dc4c4c;
          }
        }
      }

      &:hover {
        cursor: pointer;
        background-color: rgb(249, 250, 255);
      }
    }
  }

  .route-animation__slider {
    background-color: $container-bg;
    border: 1px solid $container-border;
    border-radius: 4px;
    position: absolute;
    bottom: 20px;
    width: 240px;
    left: 20px;
    padding: 8px 12px;
    z-index: 4;
    color: $container-label-color;
    font-size: $font-size-14;
    font-weight: 600;
    // transform: translateX(-50%);

    .route-animation__controls {
      .route-animation__control-container {
        padding: 6px;
        &:hover {
          cursor: pointer;
        }
      }
    }

    .time-label {
      color: $container-label-color;
      font-size: $font-size-15;
      font-family: $sub-font-family;
      font-weight: 500;
    }
  }
  .route-animation__slider2 {
    background-color: $container-bg;
    border: 1px solid $container-border;
    border-radius: 4px;
    position: absolute;
    bottom: 20px;
    width: 400px;
    right: 20px;
    padding: 4px 10px;
    z-index: 4;
    color: $container-label-color;
    font-size: $font-size-10;
    font-weight: 400;
    // transform: translateX(-50%);
  }

  .top-left-menu {
    z-index: 4;
    position: absolute;
    top: 20px;
    left: 20px;
    width: 25%;
    max-width: 45%; // Increased to accommodate device selection
    border: 1px solid $container-border;

    // Device selection dropdown styling
    .device-selection-dropdown {
      margin: 4px 0;

      .v-input__control {
        min-height: 32px;
      }

      .v-select__selection {
        font-size: 12px;
        color: #333;
      }

      .v-input__slot {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }
    .top-left-menu__divider {
      width: 100%;
      height: 1px;
      background-color: $container-border;
    }
    .top-left-menu__button {
      position: relative;

      .top-left-menu__icon-buttons {
        border-right: 1px solid black;

        .top-left-menu__icon-buttons--icon {
          &:hover {
            cursor: pointer;
          }
        }
      }

      .top-left-menu__distance-item {
        background-color: $container-bg;
        border: 1px solid $container-border;
        border-radius: 4px;
        padding: 8px 8px;
        position: absolute;
        right: -70%;
        top: 3px;
        color: $container-label-color;
        font-size: 1em;
        font-weight: 600;
        display: flex;
        flex-direction: row;
        align-items: center;
        .legend-indicator {
          width: 24px;
          height: 5px;
          background-color: #5aca5a;
          &.planned {
            background-color: #208acc;
          }
          &.selected {
            background-color: #934593;
          }
        }
        .distance-text {
          padding-left: 10px;

          // Device info text styling
          .device-info-text {
            color: #ccc;
            font-size: 10px;
            font-style: italic;
            font-weight: 400;
          }
        }
      }
    }
    .top-left-menu__distance {
      position: absolute;
      bottom: -40px;
      width: 100%;
      text-align: center;
      color: $container-label-color;
      font-size: 1.4em;
      font-weight: 600;
    }

    .eventlist--menu {
      // background-color: blue;
      max-height: 300px;
      overflow-y: scroll;
    }
  }
}
