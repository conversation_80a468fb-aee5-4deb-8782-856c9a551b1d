import RouteLink from '@/components/common/route_link.vue';
import JobDetailsEventListSummary from '@/components/operations/OperationDashboard/components/JobDetailsDialog/job_details_event_list_summary/index.vue';
import {
  JobRouteMapConfig,
  RouteViewType,
} from '@/components/operations/maps/job_map_route/JobRouteMapConfig';
import GpsPositionListDialog from '@/components/operations/maps/job_map_route/gps_position_list_dialog/index.vue';
import {
  returnDurationFromMilliseconds,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  distanceFromPoints,
  returnCenterFromGpsPositionList,
} from '@/helpers/DistanceHelpers/DistanceHelpers';
import {
  createDeviceSelectionOptions,
  getDefaultDeviceId,
  getGpsPositionsForDevice,
  groupGpsDataByDevice,
} from '@/helpers/GpsHelpers/GpsDeviceGroupingHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import GpsPosition, {
  DeviceSelectionOption,
  GpsDeviceGroup,
} from '@/interface-models/Generic/Position/GpsPosition';
import { ORSRoute } from '@/interface-models/Generic/Route/ORSRoute';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { PUDItem } from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import mapboxgl, { GeoJSONSource, LngLat, Map as MapboxMap } from 'mapbox-gl';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface HighlightBracket {
  startEpoch: number | null;
  endEpoch: number | null;
}

enum MapLayerValues {
  CAPTURED = 'captured-route-data',
  PLANNED = 'planned-route-data',
  DIALOG = 'dialog-route-data',
  CIRCLE = 'pud-area-data',
  START_END_MARKERS = 'start-end-markers',
  STOP_MARKERS = 'stop-markers',
}

enum MarkerSource {
  STOP_MARKERS = 'STOP_MARKERS',
  START_END_MARKERS = 'START_END_MARKERS',
  CLUSTERS = 'CLUSTERS',
  CLUSTER_COUNT = 'CLUSTER_COUNT',
}

enum CustomMapSwatches {
  RED = '#ce0000',
  DEEP_BLUE = '#1e3254',
  BLUE = '#208acc',
  GREEN = '#5aca5a',
  PURPLE = '#934593',
  GREY = '#BDBDBD',
}
interface MapMarkerConfig {
  id: string;
  title: string;
  sourceId: MarkerSource;
  coordinates: [number, number];
  color?: CustomMapSwatches;
  popupTitle?: string;
  popupContents?: string;
}

@Component({
  components: { JobDetailsEventListSummary, GpsPositionListDialog, RouteLink },
})
export default class JobMapRoute extends Vue {
  // jobDetails is only provided when viewing from Booking Screen or
  // JobDetailsDialog
  @Prop({ default: null }) public jobDetails: JobDetails | null;
  @Prop({ default: '' }) public mapId: string;
  @Prop({ required: true }) public mapConfig: JobRouteMapConfig;
  // @Prop({ default: false }) public gpsRouteOnly: boolean;
  // @Prop({ default: true }) public enableToggle: boolean;
  @Prop() public gpsPositionData: GpsPosition[];
  // @Prop({ default: '' }) public defaultViewType: string;
  // @Prop({ default: false }) public isClientPortal: boolean;

  public highlightBracket: HighlightBracket = {
    startEpoch: null,
    endEpoch: null,
  };
  public coordinatesForBracket: Array<[number, number]> = [];

  public mapContainerElementId: string = 'map-container';

  public selectedEventItemId: string = '';

  public viewingPlannedRouteData: boolean = false;
  public viewingPlannedRouteLine: boolean = false;
  public viewingPlannedRouteMarker: boolean = false;

  public viewingCapturedRouteData: boolean = false;
  public viewingCapturedRouteLine: boolean = false;
  public viewingCapturedRouteMarker: boolean = false;

  public viewingDialogPointData: boolean = false;
  public viewingDialogPointLine: boolean = false;
  public viewingDialogPointMarker: boolean = false;

  public defaultMapPitch: number = 55;
  public defaultMapZoom: number = 11;

  public routeAnimationTimer: any;
  public animationIterationTimes: string[] = [];
  public animationIsActive: boolean = false;
  public currentTimeLabel: string = '';
  public currentTimeIndex: number = 0;
  public currentMarkerIndex: number = 0;
  public timeMaxIndex: number = 0;

  public isViewingGpsPositionDialog: boolean = false;

  public selectedDialogPoints: Array<[number, number]> = [];
  public selectedDialogPointDistance: number = 0;

  public showStartEndLegend: boolean = false;

  public highlightDisplayTime: ReturnType<typeof setTimeout> | null = null;

  // Device grouping properties
  public selectedDeviceId: string | null = null;
  public deviceGroups: Map<string, GpsDeviceGroup> = new Map();
  public showDeviceSelection: boolean = false;

  private mapMarkerCache: Map<string, MapMarkerConfig> = new Map();
  private mapboxObject: MapboxMap | undefined;

  // Computed properties for device grouping
  get deviceSelectionOptions(): DeviceSelectionOption[] {
    return createDeviceSelectionOptions(this.deviceGroups);
  }

  get filteredGpsPositionData(): GpsPosition[] {
    return getGpsPositionsForDevice(this.deviceGroups, this.selectedDeviceId);
  }

  get hasMultipleDevices(): boolean {
    return this.deviceGroups.size > 1;
  }

  get mapLayerConfig(): JobRouteMapConfig {
    const config = { ...this.mapConfig };
    const hasCapturedRoute =
      this.filteredGpsPositionData && !!this.filteredGpsPositionData.length;
    const hasPlannedRoute = this.jobDetails && this.jobDetails.plannedRoute;
    // If the config says we should view the captured route, but there is no GPS
    // data, then we should not show the captured route
    if (config.showCapturedRoute && !hasCapturedRoute) {
      config.showCapturedRoute = false;
      config.showCapturedRouteSlider = false;
      config.enableViewToggle = false;
      if (config.defaultView === RouteViewType.CAPTURED) {
        if (hasPlannedRoute) {
          config.defaultView = RouteViewType.PLANNED;
        } else {
          config.defaultView = RouteViewType.NONE;
        }
      }
    }
    // If the config says we should view the planned route, but there is no planned
    // route data, then we should not show the planned route

    if (config.showPlannedRoute && !hasPlannedRoute) {
      config.showPlannedRoute = false;
      config.enableViewToggle = false;
      if (config.defaultView === RouteViewType.PLANNED) {
        if (hasCapturedRoute) {
          config.defaultView = RouteViewType.CAPTURED;
        } else {
          config.defaultView = RouteViewType.NONE;
        }
      }
    }
    if (!config.showCapturedRoute && !config.showPlannedRoute) {
      config.defaultView = RouteViewType.NONE;
    }

    return config;
  }

  get validDataProvided(): boolean {
    if (
      this.jobDetails &&
      this.jobDetails.pudItems.length > 1 &&
      this.jobDetails.plannedRoute &&
      this.jobDetails.plannedRoute.routes.length > 0
    ) {
      return true;
    } else {
      return false;
    }
  }

  // ==========================================================================
  // GETTER/SETTER CONTROLLERS
  // ==========================================================================
  // Return the value for highlight bracket
  get highlightBracketController(): HighlightBracket {
    return this.highlightBracket;
  }
  // Trigger data update for CAPTURED plotted line data source
  // Update to only show the points within the selected time frame as defined by HighlightBracket
  set highlightBracketController(value: HighlightBracket) {
    const coordinates = this.returnCoordinatesForBracket(
      this.filteredGpsPositionData,
      value,
    );
    this.coordinatesForBracket = coordinates;
    if (!coordinates || !coordinates.length) {
      showNotification('No GPS captured for this time period.');
      this.selectedEventItemId = '';
      return;
    }
    this.addOrUpdateSourceCoordinates(coordinates, MapLayerValues.CAPTURED);
    this.addOrUpdateStartEndMarkers(coordinates, MapLayerValues.CAPTURED);

    // Clear interval if a new menu item is selected, so we don't have to wait for previous interval to complete
    this.resetAnimationTimer();
    this.currentMarkerIndex = 0;
    this.safeRemoveMarker('PROGRESS_MARKER');
    this.highlightBracket = value;
  }

  // ===========================================================================
  // PLANNED
  // ===========================================================================
  // Controls the visibility of the Planned Route layer
  // On true value, plot the coordinates from the planned route.
  // On false, clear the layer
  get plannedRouteController() {
    return this.viewingPlannedRouteData;
  }
  set plannedRouteController(value: boolean) {
    if (!this.jobDetails) {
      return;
    }
    this.plannedRouteLineController = value;
    if (
      value ||
      !(
        this.plannedRouteMarkerController &&
        this.capturedRouteController &&
        this.mapLayerConfig.showStopMarkers
      )
    ) {
      this.plannedRouteMarkerController = value;
    }
    this.viewingPlannedRouteData = value;
  }
  // Draw or remove the planned route MARKERS
  get plannedRouteMarkerController(): boolean {
    return this.viewingPlannedRouteMarker;
  }
  set plannedRouteMarkerController(value: boolean) {
    if (value) {
      this.addOrUpdatePudItemMarkers(this.pudArrayInput);
    } else {
      const idList = this.pudArrayInput.map((pud, index) =>
        pud.pudId ? pud.pudId : `${index}`,
      );
      this.safeRemoveMarkerList(idList);
      this.removeMarkerSource(MarkerSource.STOP_MARKERS);
    }
    this.viewingPlannedRouteMarker = value;
  }
  // Draw or remove the planned route LINE
  get plannedRouteLineController(): boolean {
    return this.viewingPlannedRouteLine;
  }
  set plannedRouteLineController(value: boolean) {
    if (value) {
      this.drawPlannedRoute();
    } else {
      this.safeRemoveMapLayers(MapLayerValues.PLANNED);
    }
    this.viewingPlannedRouteLine = value;
  }

  /**
   * Refreshes the planned route by toggling the marker and line controllers.
   * Typically this is called from the parent component when the planned route
   * data has been updated, as the map will not automatically update when the
   * data changes.
   */
  public refreshPlannedRoute() {
    this.plannedRouteMarkerController = false;
    this.plannedRouteLineController = false;
    this.plannedRouteMarkerController = true;
    this.plannedRouteLineController = true;
  }

  /**
   * Called when the user removes marker layers from the map. We should check if
   * there are any layers left that need the legend. If not, then we can hide
   * the legend.
   */
  public safeRemoveStartEndLegend() {
    const startEndMarkerTypes = [
      'START',
      'END',
      'DIALOG_START',
      'DIALOG_END',
      'PROGRESS_MARKER',
    ];
    // Check if mapMarkerCache contains any of the start/end markers
    const atLeastOne = startEndMarkerTypes.some((type) =>
      this.mapMarkerCache.has(type),
    );
    // If it does not, set showStartEndLegend to false
    if (!atLeastOne) {
      this.showStartEndLegend = false;
    }
  }

  // ===========================================================================
  // CAPTURED
  // ===========================================================================
  // Controls the visibility of the Captured Route layer
  // On true value, plot the coordinates from the Job GPS Data.
  // On false, clear the layer
  get capturedRouteController() {
    return this.viewingCapturedRouteData;
  }
  set capturedRouteController(value: boolean) {
    this.capturedRouteLineController = value;
    this.capturedRouteMarkerController = value;
    if (!value) {
      this.highlightBracket.startEpoch = null;
      this.highlightBracket.endEpoch = null;
      this.selectedEventItemId = '';
    }
    this.viewingCapturedRouteData = value;
  }

  // Draw or remove the captured route MARKERS
  get capturedRouteMarkerController() {
    return this.viewingCapturedRouteMarker;
  }
  set capturedRouteMarkerController(value: boolean) {
    if (value) {
      // Draw layer
      const coordinates = this.returnCoordinatesForBracket(
        this.filteredGpsPositionData,
        this.highlightBracket,
      );
      this.coordinatesForBracket = coordinates;
      this.addOrUpdateStartEndMarkers(coordinates, MapLayerValues.CAPTURED);
    } else {
      this.safeRemoveMarkerList(['START', 'END']);
      this.removeMarkerSource(MarkerSource.START_END_MARKERS);
    }
    // If the config says we should showStopMarkers, then add them to map. If
    // the incoming value is false, then only remove them if
    // plannedRouteController is false as well
    if (
      this.mapLayerConfig.showStopMarkers &&
      (value || !this.plannedRouteController)
    ) {
      this.plannedRouteMarkerController = value;
    }
    this.viewingCapturedRouteMarker = value;
  }
  // Draw or remove the captured route LINE
  get capturedRouteLineController() {
    return this.viewingCapturedRouteLine;
  }
  set capturedRouteLineController(value: boolean) {
    if (value) {
      // Draw layer
      this.drawCapturedRoute();
    } else {
      this.resetAnimationTimer();
      this.safeRemoveMapLayers(MapLayerValues.CAPTURED);
      this.safeRemoveMarker('PROGRESS_MARKER');
      this.safeRemoveStartEndLegend();
      this.currentMarkerIndex = 0;
    }
    this.viewingCapturedRouteLine = value;
  }
  // =============================================================================
  // SELECTED POINTS FROM DIALOG
  // =============================================================================
  // Controls the visibility of the Dialog layer
  // On true value, plot the coordinates from the Job GPS Data.
  // On false, clear the layer
  get selectedDialogPointsController() {
    return this.viewingDialogPointData;
  }
  set selectedDialogPointsController(value: boolean) {
    this.selectedDialogLineController = value;
    this.selectedDialogMarkerController = value;
    this.viewingDialogPointData = value;
  }
  // Draw or remove the selected route MARKERS from dialog
  get selectedDialogMarkerController() {
    return this.viewingDialogPointMarker;
  }
  set selectedDialogMarkerController(value: boolean) {
    if (value) {
      // Draw layer
      const coordinates = this.selectedDialogPoints;
      this.addOrUpdateStartEndMarkers(coordinates, MapLayerValues.DIALOG);
    } else {
      // Clear layer
      this.safeRemoveMarkerList(['DIALOG_START', 'DIALOG_END']);
      this.removeMarkerSource(MarkerSource.START_END_MARKERS);
    }
    this.viewingDialogPointMarker = value;
  }
  // Draw or remove the selected route LINE from dialog
  get selectedDialogLineController() {
    return this.viewingDialogPointLine;
  }
  set selectedDialogLineController(value: boolean) {
    if (value) {
      // Draw layer
      this.drawDialogRoutePoints();
    } else {
      // Clear layer
      this.safeRemoveMapLayers(MapLayerValues.DIALOG);
    }
    this.viewingDialogPointLine = value;
  }
  // Use plannedRoute from jobDetails if it exists, otherwise initialise a new ORSRoute to prevent errors
  get orsRoute(): ORSRoute {
    return this.jobDetails && this.jobDetails.plannedRoute
      ? this.jobDetails.plannedRoute
      : new ORSRoute();
  }
  // Filter for valid pickup or drop-off type PUD Items
  get pudArrayInput(): PUDItem[] {
    return this.jobDetails
      ? this.jobDetails.pudItems.filter(
          (pud: PUDItem) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
        )
      : [];
  }

  // ===========================================================================
  // CLEAN LAYERS
  // ===========================================================================
  // Check if a source and layer exist for the supplied ID
  // If they exist, then remove them from the map
  public safeRemoveMapLayers(layerId: MapLayerValues) {
    if (this.mapboxObject === undefined) {
      return;
    }
    if (this.mapboxObject.getLayer(layerId)) {
      this.mapboxObject.removeLayer(layerId);
    }
    if (this.mapboxObject.getSource(layerId)) {
      this.mapboxObject.removeSource(layerId);
    }
  }
  // Remove all markers provided in idList
  public safeRemoveMarkerList(idList: string[]) {
    // Call single method for each id
    idList.forEach((id) => {
      this.safeRemoveMarker(id);
    });
    this.safeRemoveStartEndLegend();
  }
  // Remove marker for provided id from the map
  public safeRemoveMarker(id: string) {
    if (this.mapboxObject === undefined) {
      return;
    }
    // Find marker in mapMarkerCache
    // If found, remove it from the map AND from the marker cache
    const marker = this.mapMarkerCache.get(id);
    if (marker !== undefined) {
      this.mapMarkerCache.delete(id);
      this.addOrUpdateMarkerSource(marker.sourceId);
    }
  }
  // ===========================================================================
  // DRAW MAP MARKERS
  // ===========================================================================
  // Add a marker to the map for the first and last coordinate in the supplied list
  public addOrUpdateStartEndMarkers(
    coordinates: number[][],
    sourceId: MapLayerValues,
  ) {
    // Get first and last element
    const startPoint: [number, number] = coordinates[0] as [number, number];
    const endPoint: [number, number] = coordinates[coordinates.length - 1] as [
      number,
      number,
    ];
    // If data is from dialog, use the dialog id/title
    // Otherwise use generic START and END id/title
    const markerStart =
      sourceId === MapLayerValues.DIALOG ? 'DIALOG_START' : 'START';
    const markerEnd = sourceId === MapLayerValues.DIALOG ? 'DIALOG_END' : 'END';
    // Add or update marker for START point
    this.addOrUpdateMarker({
      id: markerStart,
      title: 'START',
      sourceId: MarkerSource.START_END_MARKERS,
      color:
        sourceId === MapLayerValues.DIALOG
          ? CustomMapSwatches.PURPLE
          : CustomMapSwatches.GREEN,
      coordinates: startPoint,
    });
    // Add or update marker for END point
    const jobIsCompleted =
      !this.jobDetails ||
      this.jobDetails.workStatus >= WorkStatus.DRIVER_COMPLETED;

    this.addOrUpdateMarker({
      id: markerEnd,
      title: jobIsCompleted ? 'END' : 'Last Known Location',
      sourceId: MarkerSource.START_END_MARKERS,
      color:
        sourceId === MapLayerValues.DIALOG
          ? CustomMapSwatches.PURPLE
          : jobIsCompleted
            ? CustomMapSwatches.RED
            : CustomMapSwatches.GREY,
      coordinates: endPoint,
    });
    this.addOrUpdateMarkerSource(MarkerSource.START_END_MARKERS);
    // Show legend element in html
    this.showStartEndLegend = true;
  }

  // Add a marker to the map for the first and last coordinate in the supplied list
  // Potential alternative: https://codepen.io/jackdomleo7/pen/mderEeG
  public addOrUpdatePudItemMarkers(pudItems: PUDItem[]) {
    pudItems.forEach((pud: PUDItem, index: number) => {
      const id: string = pud.pudId ? pud.pudId : `${index}`;
      const title: string = `${index + 1}`;
      const coordinates = pud.addressAsLngLat.toArray() as [number, number];
      const color = CustomMapSwatches.BLUE;
      const popupTitle: string = this.returnPudItemPopupTitle(
        pud,
        index,
        pudItems.length,
      );
      const popupContents: string = this.returnPudItemPopupContents(pud);
      this.addOrUpdateMarker({
        id,
        title,
        sourceId: MarkerSource.STOP_MARKERS,
        color,
        coordinates,
        popupTitle,
        popupContents,
      });
    });
    this.addOrUpdateMarkerSource(MarkerSource.STOP_MARKERS);
  }

  /**
   * Uses the values in the mapMarkerCache to add or update a marker on the map,
   * related to the provided sourceId. Filters for markers in the cache that
   * have the sourceId, and adds the source and layers to the map
   * @param sourceId sourceId to add or update markers for
   */
  public addOrUpdateMarkerSource(sourceId: MarkerSource): void {
    if (this.mapboxObject === undefined) {
      return;
    }
    // Get all markers for the sourceId and map them to geoJson features
    const featuresFromMarkers: GeoJSON.Feature[] = Array.from(
      this.mapMarkerCache.values(),
    )
      .filter((m) => m.sourceId === sourceId)
      .map((markerConfig) => this.createFeatureFromMarkerConfig(markerConfig));
    // If the feature list is empty, return
    if (featuresFromMarkers.length === 0) {
      return;
    }
    // If source does not exist, add it
    if (this.mapboxObject.getSource(sourceId) === undefined) {
      // Check if sourceId has clustering enabled
      const shouldCluster = this.sourceIdHasClustering(sourceId);
      this.mapboxObject.addSource(sourceId, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: featuresFromMarkers,
        },
        cluster: shouldCluster,
        clusterMaxZoom: 14,
        clusterRadius: 50,
      });
      // Add markers to the map
      const markerColor = featuresFromMarkers[0].properties
        ? featuresFromMarkers[0].properties.color
        : '';
      this.addStopMarkers(this.mapboxObject, markerColor, sourceId);

      // Add a layer for the clusters if applicable
      if (shouldCluster) {
        this.addClusterLayers(this.mapboxObject, sourceId);
      }
    } else {
      const source = this.mapboxObject.getSource(sourceId) as GeoJSONSource;
      source.setData({
        type: 'FeatureCollection',
        features: featuresFromMarkers,
      });
    }
  }

  /**
   * For the provided sourceId, remove all layers and sources from the map. If the
   * sourceId has clustering enabled, then remove the cluster layers as well.
   * @param sourceId sourceId to remove from the map
   */
  public removeMarkerSource(sourceId: MarkerSource): void {
    if (this.mapboxObject === undefined) {
      return;
    }
    // Remove layer
    if (this.mapboxObject.getLayer(sourceId)) {
      this.mapboxObject.removeLayer(sourceId);
    }
    // If the sourceId has clustering enabled, remove the cluster layers
    if (this.sourceIdHasClustering(sourceId)) {
      // Remove cluster layers
      if (this.mapboxObject.getLayer(MarkerSource.CLUSTERS)) {
        this.mapboxObject.removeLayer(MarkerSource.CLUSTERS);
      }
      if (this.mapboxObject.getLayer(MarkerSource.CLUSTER_COUNT)) {
        this.mapboxObject.removeLayer(MarkerSource.CLUSTER_COUNT);
      }
    }
    // After removing all dependant layers we can remove the source
    if (this.mapboxObject.getSource(sourceId)) {
      this.mapboxObject.removeSource(sourceId);
    }
  }

  /**
   * Adds stop markers to a Mapbox map.
   *
   * This function creates a custom icon with the specified color, adds the icon
   * to the map, and adds a layer for the individual markers. Adds hover
   * functionality to the markers to show a popup when hovered
   *
   * @param {MapboxMap} map - The Mapbox map to add the markers to.
   * @param {string} color - The color to use for the custom icon.
   * @param {string} sourceId - The ID of the source to use for the layer.
   */
  public addStopMarkers(map: MapboxMap, color: string, sourceId: MarkerSource) {
    // Create a custom icon for the markers and add it to the map if it doesn't already exist
    const markerIconName = `marker-icon-${sourceId}`;
    if (!map.hasImage(markerIconName)) {
      const markerIcon = this.createCustomMapIcon(color);
      if (markerIcon === null) {
        return;
      }
      map.addImage(markerIconName, markerIcon);
    }

    // Add a layer for the individual markers
    map.addLayer({
      id: sourceId,
      type: 'symbol',
      source: sourceId,
      filter: ['!', ['has', 'point_count']],
      layout: {
        'text-field': ['get', 'title'],
        'text-size': 13,
        'icon-image': markerIconName, // Use the custom icon
        'icon-text-fit': 'both', // Make the icon fit the text
        'icon-text-fit-padding': [4, 12, 4, 12], // Add padding around the text
      },
      paint: {
        'text-color': '#ffffff',
      },
    });

    // Create a popup, but don't add it to the map yet.
    const popup = new mapboxgl.Popup({
      closeOnClick: true,
      closeButton: true,
    });
    map.on('mouseenter', sourceId, (e) => {
      // Change the cursor style as a UI indicator.
      map.getCanvas().style.cursor = 'pointer';
      if (e.features && e.features[0].properties) {
        // Combine and title and popup contents into a single string
        const content = [
          e.features[0].properties.popupTitle
            ? `<h4 style='color: black'>${e.features[0].properties.popupTitle}</h4>`
            : '',
          e.features[0].properties.popupContents
            ? `<div style='color: black'>${e.features[0].properties.popupContents}</div>`
            : '',
        ]
          .join('')
          .trim();
        if (content) {
          popup.setLngLat(e.lngLat).setHTML(content).addTo(map);
        }
      }
    });
    map.on('mouseleave', sourceId, () => {
      map.getCanvas().style.cursor = '';
      popup.remove();
    });
  }

  /**
   * Removes any existing highlight layers and adds a highlight layer to the map
   * at the specified coordinates. The highlight is a circle with a transparent
   * fill and a colored outline. Also sets timeout to remove the highlight after
   * a period of time.
   *
   * @param  map - The Mapbox GL JS map object.
   * @param coordinates - The [longitude, latitude] coordinates where the
   * highlight should be added.
   */
  public addHighlightLayer(
    map: mapboxgl.Map,
    coordinates: [number, number],
  ): void {
    // Remove the existing highlight layer
    this.removeHighlightLayer(map);
    // Clear the timeout if it's not unset
    if (this.highlightDisplayTime !== null) {
      clearTimeout(this.highlightDisplayTime);
    }

    // Add a source for the highlight
    map.addSource('highlight-source', {
      type: 'geojson',
      data: {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'Point',
          coordinates,
        },
      },
    });

    // Add a circle layer for the highlight
    map.addLayer({
      id: 'highlight-layer',
      type: 'circle',
      source: 'highlight-source',
      paint: {
        'circle-radius': 8,
        'circle-color': 'transparent',
        'circle-stroke-width': 4,
        'circle-stroke-color': '#ff0000',
      },
    });
    // Hide the circle after 5 seconds
    this.highlightDisplayTime = setTimeout(() => {
      this.removeHighlightLayer(map);
    }, 3000);
  }

  /**
   * Removes the highlight layer from the map, if it exists.
   *
   * @param map - The Mapbox GL JS map object.
   */
  public removeHighlightLayer(map: mapboxgl.Map): void {
    // Remove the highlight layer if it exists
    if (map.getLayer('highlight-layer')) {
      map.removeLayer('highlight-layer');
      map.removeSource('highlight-source');
    }
  }

  /**
   * Moves the marker layers to the top layer of the map, such that it always
   * appears over the line layers. This is called every time we draw a line
   * layer.
   * @param map - The MapboxMap instance.
   */
  public moveMarkerLayersToTop(map: MapboxMap) {
    const markerLayers = Object.values(MarkerSource);
    markerLayers.forEach((layer) => {
      if (map.getLayer(layer)) {
        map.moveLayer(layer);
      }
    });
  }
  /**
   * Adds cluster layers to a Mapbox map.
   *
   * Adds two layers to the map: a circle layer for the clusters and a symbol
   * layer for the cluster count. Sets up event listeners for the 'mouseenter'
   * and 'mouseleave' events on the cluster layer. When the mouse enters a
   * cluster, a popup is shown with the contents of the 'popupContents' property
   * of the features in the cluster.
   *
   * @param {MapboxMap} map - The Mapbox map to add the layers to.
   * @param {string} sourceId - The ID of the source to use for the layers.
   */
  public addClusterLayers(map: MapboxMap, sourceId: string) {
    // Add a layer for the clusters
    map.addLayer({
      id: MarkerSource.CLUSTERS,
      type: 'circle',
      source: sourceId,
      filter: ['has', 'point_count'],
      paint: {
        'circle-color': CustomMapSwatches.BLUE,
        'circle-radius': 16,
        'circle-stroke-color': CustomMapSwatches.DEEP_BLUE,
        'circle-stroke-width': 4,
      },
    });

    map.addLayer({
      id: MarkerSource.CLUSTER_COUNT,
      type: 'symbol',
      source: sourceId,
      filter: ['has', 'point_count'],
      layout: {
        'text-field': '{point_count_abbreviated}',
        'text-font': ['Arial Unicode MS Bold'],
        'text-size': 14,
      },
      paint: {
        'text-color': '#ffffff', // Set the text color to white
      },
    });

    const popup = new mapboxgl.Popup({
      closeButton: false,
      closeOnClick: false,
    });
    map.on('mouseenter', MarkerSource.CLUSTERS, (e) => {
      // Change the cursor style as a UI indicator.
      map.getCanvas().style.cursor = 'pointer';
      if (e.features && e.features[0].properties) {
        const clusterId = e.features[0].properties.cluster_id;
        (map.getSource(sourceId) as mapboxgl.GeoJSONSource).getClusterLeaves(
          clusterId,
          10,
          0,
          (err, features) => {
            if (err) {
              throw err;
            }

            const popupContents = features.map((feature) => {
              // Assuming 'marker-symbol' contains the desired information
              return feature.properties
                ? `<li>${feature.properties.popupTitle}</li>`
                : '';
            });
            const content = `<h4 style='color: black'>${
              popupContents.length
            } Stops</h4><ol style='color: black'>${popupContents.join(
              '',
            )}</ol>`;
            popup.setLngLat(e.lngLat).setHTML(content).addTo(map);
          },
        );
      }
    });

    map.on('mouseleave', MarkerSource.CLUSTERS, () => {
      map.getCanvas().style.cursor = '';
      popup.remove();
    });
  }

  public createCustomMapIcon(color?: string): ImageData | null {
    // Create a new HTML canvas
    const canvas = document.createElement('canvas');
    canvas.width = color === CustomMapSwatches.BLUE ? 24 : 80; // Set the width of the canvas
    canvas.height = 24; // Set the height of the canvas

    // Get the 2D rendering context of the canvas
    const context = canvas.getContext('2d');

    if (context === null) {
      return null;
    }

    // Define the parameters for the outer rectangle
    const outerX = 0; // The x-coordinate of the upper-left corner of the rectangle
    const outerY = 0; // The y-coordinate of the upper-left corner of the rectangle
    const outerWidth = canvas.width; // The width of the rectangle
    const outerHeight = canvas.height; // The height of the rectangle
    const outerRadius = 6; // The radius of the corners

    // Draw the outer rectangle
    context.beginPath(); // Start a new path
    context.moveTo(outerX + outerRadius, outerY); // Move the pen to the starting point of the rounded rectangle
    context.arcTo(
      outerX + outerWidth,
      outerY,
      outerX + outerWidth,
      outerY + outerHeight,
      outerRadius,
    ); // Draw the top-right corner
    context.arcTo(
      outerX + outerWidth,
      outerY + outerHeight,
      outerX,
      outerY + outerHeight,
      outerRadius,
    ); // Draw the bottom-right corner
    context.arcTo(outerX, outerY + outerHeight, outerX, outerY, outerRadius); // Draw the bottom-left corner
    context.arcTo(outerX, outerY, outerX + outerWidth, outerY, outerRadius); // Draw the top-left corner
    context.closePath(); // Close the path
    context.fillStyle = '#2d2d2d'; // Set the stroke color
    context.lineWidth = 4; // Set the line width to 4 pixels
    context.stroke(); // Stroke the rounded rectangle

    // Draw the custom icon
    context.fillStyle = color ? color : '#1976d2'; // Set the fill color'
    context.strokeStyle = '#ffffff'; // Set the stroke color
    context.lineWidth = 2; // Set the line width to 2 pixels

    // Define the parameters for the rounded rectangle
    const x = 2; // The x-coordinate of the upper-left corner of the rectangle
    const y = 2; // The y-coordinate of the upper-left corner of the rectangle
    const width = canvas.width - 4; // The width of the rectangle
    const height = canvas.height - 4; // The height of the rectangle
    const radius = 2; // The radius of the corners

    // Draw the rounded rectangle
    context.beginPath(); // Start a new path
    context.moveTo(x + radius, y); // Move the pen to the starting point of the rounded rectangle
    context.arcTo(x + width, y, x + width, y + height, radius); // Draw the top-right corner
    context.arcTo(x + width, y + height, x, y + height, radius); // Draw the bottom-right corner
    context.arcTo(x, y + height, x, y, radius); // Draw the bottom-left corner
    context.arcTo(x, y, x + width, y, radius); // Draw the top-left corner
    context.closePath(); // Close the path
    context.fill();
    context.stroke();

    // Return the image data
    return context.getImageData(0, 0, canvas.width, canvas.height);
  }

  /**
   * Converts a marker configuration to a GeoJSON feature.
   *
   * @param markerConfig - The marker configuration to convert.
   * @returns  The converted GeoJSON feature.
   */
  public createFeatureFromMarkerConfig(
    markerConfig: MapMarkerConfig,
  ): GeoJSON.Feature {
    return {
      type: 'Feature',
      properties: {
        id: markerConfig.id,
        title: markerConfig.title,
        color: markerConfig.color,
        popupTitle: markerConfig.popupTitle,
        popupContents: markerConfig.popupContents,
      },
      geometry: {
        type: 'Point',
        coordinates: markerConfig.coordinates,
      },
    };
  }

  /**
   * Checks if a source ID corresponds to a source with clustering enabled.
   * @param sourceId - The source ID to check.
   * @returns  True if the source ID corresponds to a source with clustering enabled, false otherwise.
   */
  public sourceIdHasClustering(sourceId: MarkerSource): boolean {
    return sourceId === MarkerSource.STOP_MARKERS;
  }

  // Return description of pud item to be used as the contents of a Mapbox Popup on the marker
  public returnPudItemPopupTitle(
    pudItem: PUDItem,
    index: number,
    totalCount: number,
  ): string {
    return `${pudItem.legTypeFlag === 'P' ? 'PICKUP' : 'DELIVERY'} (stop ${
      index + 1
    } of ${totalCount})`;
  }
  // Return description of pud item to be used as the contents of a Mapbox Popup on the marker
  public returnPudItemPopupContents(pudItem: PUDItem): string {
    const boldString = (str: string): string => `<b>${str}</b>`;
    const lineItem = (key: string, value: string): string =>
      `${boldString(key)}: ${value} <br />`;

    const lines = [
      ['Address', pudItem.address.formattedAddress],
      ['Location Name', pudItem.customerDeliveryName || '-'],
      [
        'Est. Time',
        pudItem.epochTime ? returnFormattedTime(pudItem.epochTime) : '-',
      ],
      [
        'Est. Load',
        pudItem.loadTime
          ? returnDurationFromMilliseconds(pudItem.loadTime)
          : '-',
      ],
      [
        'Contact',
        `${pudItem.siteContactName || '-'} / ${
          pudItem.siteContactLandLineNumber || '-'
        } / ${pudItem.siteContactMobileNumber || '-'}`,
      ],
    ];

    return lines.map(([key, value]) => lineItem(key, value)).join('');
  }

  // Creates or update a marker based on the supplied config values
  // markerConfig.id will be used as the key in the marker cache map
  // Other markerConfig values will change marker appearance and location on map
  public addOrUpdateMarker(markerConfig: MapMarkerConfig) {
    if (markerConfig.coordinates === undefined) {
      return;
    }
    // If a marker already exists on the map, it will be in the mapMarkerCache
    // Check for  marker in cache
    if (this.mapMarkerCache.has(markerConfig.id)) {
      const marker = this.mapMarkerCache.get(markerConfig.id);
      marker!.coordinates = markerConfig.coordinates;
    } else {
      this.mapMarkerCache.set(markerConfig.id, markerConfig);
    }
  }
  // Construct HTML tooltip element using the supplier MarkerConfig
  public returnMarkerElement(markerConfig: MapMarkerConfig): HTMLDivElement {
    const el: HTMLDivElement = document.createElement('div');
    const tailElement2: HTMLDivElement = document.createElement('div');
    tailElement2.className = 'job-map-route--marker__tail-1';

    el.className = 'job-map-route--marker';
    el.innerHTML = markerConfig.title;

    el.style.backgroundColor = markerConfig.color
      ? markerConfig.color
      : CustomMapSwatches.BLUE;

    el.appendChild(tailElement2);

    return el;
  }

  // =============================================================================
  // DRAW ROUTE
  // =============================================================================
  public drawPlannedRoute(): void {
    const route: ORSRoute = this.orsRoute;
    let coordinates: number[][] = [];
    if (route && route.routes[0]) {
      coordinates = route.routes[0].geometry.coordinates
        ? route.routes[0].geometry.coordinates
        : [];
    }
    this.drawLineFromCoordinates(coordinates, MapLayerValues.PLANNED);
  }
  // Using the filteredGpsPositionData, create an array of coordinates to drawn onto the map
  public drawCapturedRoute(): void {
    // If we have a currently highlighted bracket, then we should use those points as the focal point of the map. Otherwise we should just use the full list of points
    const coordinates = this.returnCoordinatesForBracket(
      this.filteredGpsPositionData,
      this.highlightBracket,
    );
    this.drawLineFromCoordinates(coordinates, MapLayerValues.CAPTURED);
  }
  // Using the points passed down from the dialog component, draw the points to the map
  public drawDialogRoutePoints(): void {
    const coordinates = this.selectedDialogPoints;
    this.drawLineFromCoordinates(coordinates, MapLayerValues.DIALOG);
  }

  // Find the source for the supplied sourceId
  // If not found, add the source using the supplied coordinates
  // If found, call setData to update values
  public addOrUpdateSourceCoordinates(
    coordinates: number[][],
    sourceId: MapLayerValues,
  ): void {
    if (this.mapboxObject === undefined) {
      return;
    }
    // Construct FeatureCollection using coordinates
    const featureCollection: GeoJSON.FeatureCollection = {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates,
          },
        },
      ],
    };
    // Get the source for sourceId and coerce to GeoJSONSource type from dynamic type as we use exclusively geojson
    const source: GeoJSONSource = this.mapboxObject.getSource(
      sourceId,
    ) as GeoJSONSource;
    // If no source exists, add it
    if (source === undefined) {
      this.mapboxObject.addSource(sourceId, {
        type: 'geojson',
        lineMetrics: true,
        data: featureCollection,
      });
    } else {
      // If source exists, update data
      source.setData(featureCollection);
    }
    // // Add start and end markers if required (used for Captured Route type)
    // if (includeStartEndMarkers && coordinates.length > 0) {
    //   this.addOrUpdateStartEndMarkers(coordinates, sourceId);
    // }
    // Fit map to coordinate list
    this.fitMapToBounds(coordinates);
  }

  // Draw a line with the supplied coordinates
  // Set the source and layer ids as the supplied layerId value
  public drawLineFromCoordinates(
    coordinates: number[][],
    layerId: MapLayerValues,
  ): void {
    if (this.mapboxObject === undefined) {
      return;
    }

    if (this.mapboxObject.getLayer(layerId) === undefined) {
      this.addOrUpdateSourceCoordinates(coordinates, layerId);
      this.mapboxObject.addLayer({
        id: layerId,
        type: 'line',
        source: layerId,
        layout: {
          'line-cap': 'round',
          'line-join': 'miter',
        },
        paint: this.returnLinePaintForLayerId(layerId),
      });
      // Move marker layers to the top
      this.moveMarkerLayersToTop(this.mapboxObject);
    }
  }
  // Returns styles of line to be plotted, based on whether we are plotting the Planned or Captured route
  public returnLinePaintForLayerId(
    layerId: MapLayerValues,
  ): mapboxgl.LinePaint | undefined {
    if (layerId === MapLayerValues.PLANNED) {
      return {
        'line-color': CustomMapSwatches.BLUE,
        'line-width': 6,
        'line-offset': 4,
        'line-dasharray': [1, 1],
      };
    } else if (layerId === MapLayerValues.CAPTURED) {
      return {
        'line-color': CustomMapSwatches.DEEP_BLUE,
        'line-width': 6,
        // 'line-gradient' must be specified using an expression
        // with the special 'line-progress' property
        'line-gradient': [
          'interpolate',
          ['linear'],
          ['line-progress'],
          0,
          CustomMapSwatches.GREEN,
          0.01,
          CustomMapSwatches.DEEP_BLUE,
          0.99,
          CustomMapSwatches.DEEP_BLUE,
          1,
          CustomMapSwatches.RED,
        ],
      };
    }
    if (layerId === MapLayerValues.DIALOG) {
      return {
        'line-color': CustomMapSwatches.PURPLE,
        'line-width': 6,
        'line-offset': 4,
        'line-dasharray': [1, 1],
      };
    }
  }
  // Handle emit from dialog component
  // Points reflect point selection from dialog window, to be plotted on map with markers
  public showSelectedRangeOnMap(gpsData: GpsPosition[]) {
    this.plannedRouteController = false;
    this.capturedRouteController = false;
    this.selectedDialogPointDistance = distanceFromPoints(gpsData);
    this.selectedDialogPoints = gpsData.map((gps) => {
      return gps.asLngLat.toArray() as [number, number];
    });
    this.selectedDialogPointsController = true;
  }

  // ===========================================================================
  // LINE ANIMATION
  // ===========================================================================

  // Create map feature from the current GPS data and highlightBracket
  // Pass into animateRoute method to step plot the points on the map
  public animateCapturedRoute(startIndex: number = 0) {
    const coordinates = this.coordinatesForBracket;
    if (coordinates.length < 4) {
      showNotification('Not enough data available to display route.');
      return;
    }

    if (coordinates.length > 0) {
      // Construct FeatureCollection using coordinates
      const featureCollection: GeoJSON.FeatureCollection = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'LineString',
              coordinates,
            },
          },
        ],
      };
      this.animateRoute(
        featureCollection,
        MapLayerValues.CAPTURED,
        this.animationIterationTimes,
        startIndex,
      );
    }
  }
  // Plot coordinates in supplied featureCollection one by one
  public animateRoute(
    featureCollection: GeoJSON.FeatureCollection,
    sourceId: MapLayerValues,
    timeArray: string[],
    startIndex: number,
  ) {
    if (this.mapboxObject === undefined) {
      return;
    }
    // Clear interval if a new menu item is selected, so we don't have to wait for previous interval to complete
    if (this.routeAnimationTimer !== null) {
      this.resetAnimationTimer();
    }
    const map = this.mapboxObject;
    this.animationIterationTimes = timeArray;
    // Take copy of coordinates so we don't mutate original data
    const coordinates: number[][] = JSON.parse(
      JSON.stringify(
        (featureCollection.features[0].geometry as GeoJSON.LineString)
          .coordinates,
      ),
    );
    // Set feature details to empty list
    (featureCollection.features[0].geometry as GeoJSON.LineString).coordinates =
      coordinates.slice(0, startIndex);

    const source: GeoJSONSource = map.getSource(sourceId) as GeoJSONSource;
    // Set interval which adds a single point to the map source with each step
    // Call setData on each iteration to update map source with new points
    let i = startIndex;
    this.animationIsActive = true;
    this.routeAnimationTimer = setInterval(() => {
      if (i < coordinates.length) {
        (
          featureCollection.features[0].geometry as GeoJSON.LineString
        ).coordinates.push(coordinates[i]);
        source.setData(featureCollection);
        i++;
        this.currentTimeLabel = this.animationIterationTimes[i]
          ? this.animationIterationTimes[i]
          : '';
        this.currentTimeIndex = i;
      } else {
        // Clear interval when we have added all of the points
        this.resetAnimationTimer();
      }
    }, 20);
  }
  // Resume the animation timer from the last known index
  public startAnimationTimer() {
    this.fitMapToBounds(this.coordinatesForBracket, 0);
    this.animateCapturedRoute(this.currentTimeIndex);
  }
  // Pause animation timer at current location
  public pauseAnimationTimer() {
    if (this.routeAnimationTimer) {
      clearInterval(this.routeAnimationTimer);
      this.routeAnimationTimer = null;
    }
    this.animationIsActive = false;
  }
  // Stop the animation and reset the current captured route to the full route for the bracket
  public stopAnimationTimer() {
    this.resetAnimationTimer();
    this.addOrUpdateSourceCoordinates(
      this.coordinatesForBracket,
      MapLayerValues.CAPTURED,
    );
  }
  // Add or update the marker controlled by the slider
  public updateRouteProgressMarker(index: number) {
    if (this.mapboxObject === undefined) {
      return;
    }
    const coordinates = this.coordinatesForBracket[index] as [number, number];
    // const markerId = 'PROGRESS_MARKER';
    // // If a marker already exists on the map, it will be in the mapMarkerCache
    // // Check for  marker in cache
    // if (this.mapMarkerCache.has(markerId)) {
    //   const marker = this.mapMarkerCache.get(markerId);
    //   marker!.coordinates = coordinates;
    // } else {
    //   this.mapMarkerCache.set(markerId, {
    //     id: markerId,
    //     title: 'V',
    //     sourceId: MarkerSource.START_END_MARKERS,
    //     coordinates
    //   });
    // }
    // this.addOrUpdateMarkerSource(MarkerSource.START_END_MARKERS);
    this.addHighlightLayer(this.mapboxObject, coordinates);
  }

  // Construct HTML tooltip element using the supplier MarkerConfig
  public returnCircleElement(): HTMLDivElement {
    const el: HTMLDivElement = document.createElement('div');
    el.className = 'job-map-route--marker-circle';
    return el;
  }

  // Clear interview and reset variables used in the animation
  public resetAnimationTimer() {
    if (this.routeAnimationTimer) {
      clearInterval(this.routeAnimationTimer);
      this.routeAnimationTimer = null;
    }
    this.animationIsActive = false;
    this.currentTimeLabel = '';
    this.currentTimeIndex = 0;
  }

  // Accepts list of GpsPosition, filters the list based on the current highlight bracket properties
  // The filtered list is returned
  public returnCoordinatesForBracket(
    gpsPositionData: GpsPosition[],
    highlightBracket: HighlightBracket,
  ): Array<[number, number]> {
    let gpsData: GpsPosition[] = [];
    if (
      highlightBracket &&
      highlightBracket.startEpoch &&
      highlightBracket.endEpoch
    ) {
      const startEpoch: number = highlightBracket.startEpoch;
      const endEpoch: number = highlightBracket.endEpoch;

      gpsData = gpsPositionData.filter(
        (g) => g.epochTime <= endEpoch && g.epochTime >= startEpoch,
      );
    } else {
      gpsData = gpsPositionData;
    }

    this.animationIterationTimes = gpsData.map((gps) =>
      returnFormattedTime(gps.epochTime, 'HH:mm'),
    );

    const coordinates = gpsData.map((gps) => {
      return gps.asLngLat.toArray() as [number, number];
    });
    this.timeMaxIndex = coordinates.length;

    return coordinates;
  }
  // Sets the bracket value from what is emitted from the Event List component
  public setHighlightBracket(value: HighlightBracket): void {
    this.highlightBracketController = value;
  }

  /**
   * Centers the map at the start or the end of the bracket. Modelled to the
   * legend in the html
   * @param {string} routeType - 'CAPTURED' or 'DIALOG'. CAPTURED refers to
   * captured route, DIALOG refers to custom selection from the
   * GpsPositionListDialog
   * @param {string} position - 'START' or 'END', indicating where we're jumping
   * to the beginning or end coordinate from the bracket
   */
  public jumpToBracketPosition(
    routeType: 'CAPTURED' | 'DIALOG',
    position: 'START' | 'END',
  ) {
    if (
      (routeType === 'CAPTURED' &&
        (!this.coordinatesForBracket || !this.coordinatesForBracket.length)) ||
      (routeType === 'DIALOG' &&
        (!this.selectedDialogPoints || !this.selectedDialogPoints.length)) ||
      !this.mapboxObject
    ) {
      return;
    }
    const map = this.mapboxObject;
    // Use the routeType to determine which list of coordinates we'll use
    const coordsToUse =
      routeType === 'CAPTURED'
        ? this.coordinatesForBracket
        : this.selectedDialogPoints;
    // Use position to determine whether we're jumping to the first or last in
    // the coordsToUse list
    const coordinates =
      position === 'START'
        ? coordsToUse[0]
        : coordsToUse[coordsToUse.length - 1];
    // Jump to location on map
    this.centerMapToPoint(map, coordinates);
    // Add a new highlight layer
    this.addHighlightLayer(map, coordinates);
  }

  // Adjust map view and zoom level to fit the coordinates provided
  public fitMapToBounds(coordinates: number[][], padding: number = 100) {
    if (this.mapboxObject === undefined) {
      return;
    }
    if (coordinates.length === 0) {
      console.error('No coordinates in fitMapToBounds');
      return;
    }
    // Create bounds object with no values
    const bounds = new mapboxgl.LngLatBounds();
    // Push the empty bounds outwards for each of the supplied coordinates
    // The result will be a bounding box that encloses all coordinates
    coordinates.forEach((coord: number[]) => {
      bounds.extend(coord as [number, number]);
    });
    // Set the map bounds to the created bounds object
    this.mapboxObject.fitBounds(bounds, {
      linear: true,
      maxZoom: 13,
      maxDuration: 1000,
      padding,
    });
  }

  /**
   * Centers the map to the provided coordinates, with option to set zoom level
   * @param point coordinates to center on
   * @param zoom optional parameter to adjust zoom
   */
  public centerMapToPoint(map: MapboxMap, point: [number, number]): void {
    if (!map.loaded()) {
      return;
    }
    map.jumpTo({ center: point });
  }

  // Find center point from array of GPS coordinates
  public returnMapCenterFromGps(gpsPositionList: GpsPosition[]) {
    return returnCenterFromGpsPositionList(gpsPositionList);
  }

  // Return center point from bounding box in ORS Route
  public returnMapCenterFromRoute(route: ORSRoute): LngLat {
    return new LngLat(
      route.bbox[0] + (route.bbox[2] - route.bbox[0]) / 2,
      route.bbox[1] + (route.bbox[3] - route.bbox[1]) / 2,
    );
  }

  get totalCapturedDistance() {
    if (this.filteredGpsPositionData) {
      return distanceFromPoints(this.filteredGpsPositionData);
    }
  }
  get totalPlannedDistance() {
    if (this.orsRoute) {
      return this.orsRoute.routes[0].summary.distance;
    }
  }

  // Device grouping methods
  public initializeDeviceGrouping(): void {
    if (!this.gpsPositionData || this.gpsPositionData.length === 0) {
      this.deviceGroups.clear();
      this.selectedDeviceId = null;
      this.showDeviceSelection = false;
      return;
    }

    // Group GPS data by deviceId
    this.deviceGroups = groupGpsDataByDevice(this.gpsPositionData);

    // Determine if we should show device selection
    this.showDeviceSelection = this.hasMultipleDevices;

    // Set default selected device (the one with most data points)
    this.selectedDeviceId = getDefaultDeviceId(this.deviceGroups);
  }

  public onDeviceSelectionChange(deviceId: string): void {
    this.selectedDeviceId = deviceId;

    // Refresh the map with the new device data
    this.$nextTick(() => {
      if (this.capturedRouteController) {
        this.drawCapturedRoute();
      }
    });
  }

  private initialiseMap() {
    mapboxgl.baseApiUrl = 'https://maps.gode.st/';
    this.mapboxObject = new mapboxgl.Map({
      container: this.mapId ? this.mapId : 'map-container',
      center: this.validDataProvided
        ? this.returnMapCenterFromRoute(this.orsRoute)
        : useCompanyDetailsStore().divisionDepotCoordinates,
      zoom: this.defaultMapZoom,
      interactive: true,
      attributionControl: true,
      style: './maps/goDesta.json',
      pitch: this.defaultMapPitch,
    });

    this.mapboxObject.addControl(new mapboxgl.NavigationControl(), 'top-right');
    this.mapboxObject.addControl(new mapboxgl.FullscreenControl(), 'top-right');
    this.mapboxObject.addControl(
      new mapboxgl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true,
        },
        trackUserLocation: false,
      }),
      'top-right',
    );

    if (this.mapboxObject === undefined) {
      return;
    }

    this.mapboxObject.on('load', () => {
      if (this.mapLayerConfig.defaultView === RouteViewType.CAPTURED) {
        this.plannedRouteController = false;
        this.capturedRouteController = true;
      } else if (this.mapLayerConfig.defaultView === RouteViewType.PLANNED) {
        this.plannedRouteController = true;
        this.capturedRouteController = false;
      } else {
        this.plannedRouteController = false;
        this.capturedRouteController = false;
      }
    });

    this.addRouteLinkToMapControl();
  }

  // injects third party routing link (RouteLink component) into control elements in mapbox
  private addRouteLinkToMapControl(): void {
    if (sessionManager.getSecurityLevel() === 'Client') {
      return;
    }
    const routeLinkParentControlContainer = document.createElement('div');
    routeLinkParentControlContainer.className =
      'mapboxgl-ctrl mapboxgl-ctrl-group routeLink';

    const mapBoxTopRightControlElement = document.getElementsByClassName(
      'mapboxgl-ctrl-top-right',
    );

    if (!mapBoxTopRightControlElement) {
      return;
    }

    mapBoxTopRightControlElement[0].appendChild(
      routeLinkParentControlContainer,
    );
    new Vue({
      render: (h) =>
        h(RouteLink, {
          props: {
            pudItems: this.jobDetails ? this.jobDetails.pudItems : [],
          },
        }),
    }).$mount('.routeLink');
  }

  private mounted() {
    this.initialiseMap();
    this.initializeDeviceGrouping();
  }

  // Watch for changes in GPS position data to reinitialize device grouping
  private gpsPositionDataWatcher = this.$watch(
    'gpsPositionData',
    () => {
      this.initializeDeviceGrouping();
    },
    { immediate: false },
  );

  private beforeDestroy() {
    if (this.routeAnimationTimer) {
      this.resetAnimationTimer();
    }
    // Clean up watcher
    if (this.gpsPositionDataWatcher) {
      this.gpsPositionDataWatcher();
    }
  }
}
