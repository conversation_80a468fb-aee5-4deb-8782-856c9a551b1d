<div :id="mapId ? mapId : 'map-container'" class="map-route">
  <div class="top-left-menu" v-if="mapLayerConfig.enableViewToggle">
    <v-list class="v-list-custom" dense>
      <v-list-tile class="top-left-menu__button" v-if="jobDetails">
        <div class="top-left-menu__distance-item" v-if="plannedRouteController">
          <span class="top-left-menu__icon-buttons pr-1 mr-2">
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="plannedRouteMarkerController = !plannedRouteMarkerController"
            >
              <v-icon
                size="14"
                :color="plannedRouteMarkerController ? '#208acc' : 'grey'"
              >
                fas fa-map-marker</v-icon
              >
            </span>
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="plannedRouteLineController = !plannedRouteLineController"
            >
              <v-icon
                size="14"
                :color="plannedRouteLineController ? '#208acc' : 'grey'"
              >
                fas fa-route</v-icon
              >
            </span>
          </span>
          <span class="legend-indicator planned"></span>
          <span class="distance-text">
            {{(totalPlannedDistance/1000).toFixed(2)}} km
          </span>
        </div>
        <v-list-tile-avatar>
          <v-checkbox
            v-model="plannedRouteController"
            color="#208acc"
          ></v-checkbox>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Planned </v-list-tile-title>
      </v-list-tile>
      <v-divider v-if="jobDetails"></v-divider>

      <!-- Device Selection Dropdown -->
      <v-list-tile
        class="top-left-menu__button"
      >
        <v-list-tile-content>
          <v-select
            v-model="selectedDeviceId"
            :items="deviceSelectionOptions"
            item-text="text"
            item-value="value"
            label="Select Device"
            dense
            solo
            hide-details
            class="device-selection-dropdown"
            @change="onDeviceSelectionChange"
          ></v-select>
        </v-list-tile-content>
      </v-list-tile>
      <v-divider v-if="showDeviceSelection && hasMultipleDevices"></v-divider>

      <v-list-tile
        class="top-left-menu__button"
        v-if="filteredGpsPositionData && filteredGpsPositionData.length > 0"
      >
        <v-list-tile-avatar>
          <v-checkbox
            v-model="capturedRouteController"
            color="#5aca5a"
          ></v-checkbox>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Route Taken </v-list-tile-title>
        <v-list-tile-action> </v-list-tile-action>
        <div
          class="top-left-menu__distance-item"
          v-if="capturedRouteController"
        >
          <span class="top-left-menu__icon-buttons pr-1 mr-2">
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="capturedRouteMarkerController = !capturedRouteMarkerController"
            >
              <v-icon
                size="14"
                :color="capturedRouteMarkerController ? '#5aca5a' : 'grey'"
              >
                fas fa-map-marker</v-icon
              >
            </span>
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="capturedRouteLineController = !capturedRouteLineController"
            >
              <v-icon
                size="14"
                :color="capturedRouteLineController ? '#5aca5a' : 'grey'"
              >
                fas fa-route</v-icon
              >
            </span>
          </span>
          <span class="legend-indicator"></span>
          <span class="distance-text">
            {{(totalCapturedDistance/1000).toFixed(2)}} km
            <!-- Show selected device info if multiple devices -->
            <span v-if="hasMultipleDevices && selectedDeviceId" class="device-info-text">
              <br><small>({{ deviceGroups.get(selectedDeviceId)?.displayName || 'Selected Device' }})</small>
            </span>
          </span>
        </div>
      </v-list-tile>
      <v-divider v-if="selectedDialogPointsController"></v-divider>
      <v-list-tile
        class="top-left-menu__button"
        :class="selectedDialogPointsController ? 'activated' : ''"
        v-if="selectedDialogPointsController"
      >
        <div class="top-left-menu__distance-item">
          <span class="top-left-menu__icon-buttons pr-1 mr-2">
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="selectedDialogMarkerController = !selectedDialogMarkerController"
            >
              <v-icon
                size="14"
                :color="selectedDialogMarkerController ? '#934593' : 'grey'"
              >
                fas fa-map-marker</v-icon
              >
            </span>
            <span
              class="pa-2 top-left-menu__icon-buttons--icon"
              @click="selectedDialogLineController = !selectedDialogLineController"
            >
              <v-icon
                size="14"
                :color="selectedDialogLineController ? '#934593' : 'grey'"
              >
                fas fa-route</v-icon
              >
            </span>
          </span>
          <span class="legend-indicator selected"></span>
          <span class="distance-text">
            {{(selectedDialogPointDistance/1000).toFixed(2)}} km
          </span>
        </div>

        <v-list-tile-avatar>
          <v-checkbox
            color="#934593"
            v-model="selectedDialogPointsController"
          ></v-checkbox>
        </v-list-tile-avatar>
        <v-list-tile-title class="pr-2"> Selected Points </v-list-tile-title>
      </v-list-tile>
    </v-list>
    <div
      class="eventlist--menu app-bgcolor--300"
      v-if="capturedRouteController"
    >
      <v-layout justify-space-between align-center>
        <span class="pl-3" style="text-transform: uppercase; font-weight: 700"
          >{{!jobDetails ? '' : 'Stops'}}</span
        >
        <v-spacer></v-spacer>
        <span>
          <v-menu right style="z-index: 100000">
            <template v-slot:activator="{ on: menu }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on: tooltip }">
                  <v-btn flat icon v-on="{ ...tooltip, ...menu }" class="ma-0">
                    <v-icon size="14">fas fa-ellipsis-v </v-icon>
                  </v-btn>
                </template>
                <span>View Additional Actions</span>
              </v-tooltip>
            </template>
            <v-list dense class="v-list-custom">
              <v-list-tile @click="isViewingGpsPositionDialog = true">
                <v-list-tile-avatar>
                  <v-icon size="14">fas fa-map-marker-alt</v-icon>
                </v-list-tile-avatar>
                <v-list-tile-title class="pr-2">
                  View GPS History
                </v-list-tile-title>
              </v-list-tile>
            </v-list>
          </v-menu>
        </span>
      </v-layout>
      <JobDetailsEventListSummary
        v-if="jobDetails"
        :jobDetails="jobDetails"
        :showTitle="false"
        :showActionedBy="false"
        :showChatMessages="false"
        :showJobEvents="false"
        :showTravelDuration="true"
        :showOnSiteDuration="true"
        :showIndividualPudEvents="false"
        :allowSelection="true"
        :denseList="true"
        :selectedEventItemId.sync="selectedEventItemId"
        @setHighlightBracket="setHighlightBracket"
      >
      </JobDetailsEventListSummary>
    </div>
  </div>

  <v-layout column class="map-route-legend" v-if="showStartEndLegend">
    <div class="legend-section" v-if="capturedRouteController">
      <div class="legend-subtitle">Captured Route</div>
      <div
        px-2
        class="legend-item"
        @click="jumpToBracketPosition('CAPTURED', 'START')"
      >
        <span class="legend-item-label start-type"> Start </span>
      </div>
      <div
        px-2
        class="legend-item"
        @click="jumpToBracketPosition('CAPTURED', 'END')"
      >
        <span class="legend-item-label end-type"> Finish </span>
      </div>
    </div>
    <div class="legend-section" v-if="selectedDialogPointsController">
      <div class="legend-subtitle">Custom Selection</div>
      <div
        px-2
        class="legend-item"
        @click="jumpToBracketPosition('DIALOG', 'START')"
      >
        <span class="legend-item-label start-type"> Start </span>
      </div>
      <div
        px-2
        class="legend-item"
        @click="jumpToBracketPosition('DIALOG', 'END')"
      >
        <span class="legend-item-label end-type"> Finish </span>
      </div>
    </div>
  </v-layout>

  <div
    class="route-animation__slider"
    v-if="capturedRouteLineController && mapLayerConfig.showCapturedRouteSlider"
  >
    <v-flex xs12>
      <v-layout align-center>
        <span class="time-label"
          >{{currentTimeLabel ? currentTimeLabel : 'Animate Route'}}</span
        >
        <v-spacer></v-spacer>
        <div class="route-animation__controls">
          <span
            v-if="animationIsActive || currentTimeIndex !== 0"
            class="route-animation__control-container"
            @click="stopAnimationTimer"
          >
            <v-icon color="#5aca5a" size="20">fas fa-stop</v-icon>
          </span>
          <span
            v-if="!animationIsActive"
            class="route-animation__control-container"
            @click="startAnimationTimer"
          >
            <v-icon color="#5aca5a" size="20">fas fa-play</v-icon>
          </span>
          <span
            v-else
            class="route-animation__control-container"
            @click="pauseAnimationTimer"
          >
            <v-icon color="#5aca5a" size="20">fas fa-pause</v-icon>
          </span>
        </div>
      </v-layout>
    </v-flex>
  </div>
  <div
    class="route-animation__slider2"
    v-if="capturedRouteLineController && !animationIsActive && mapLayerConfig.showCapturedRouteSlider"
  >
    <v-flex xs12>
      <v-slider
        v-model="currentMarkerIndex"
        :min="0"
        :max="timeMaxIndex - 1"
        track-color="grey darken-2"
        hide-details
        thumb-label="always"
        thumb-size="40"
        class="pa-0 ma-0"
        @change="updateRouteProgressMarker"
      >
        <template v-slot:thumb-label="item">
          {{animationIterationTimes[item.value]}}
        </template>
        <template v-slot:prepend>
          <span style="font-size: 13px; padding-top: 4px"
            >{{animationIterationTimes[0]}}</span
          >
        </template>

        <template v-slot:append>
          <span style="font-size: 13px; padding-top: 4px"
            >{{animationIterationTimes[animationIterationTimes.length -
            1]}}</span
          >
        </template>
      </v-slider>
    </v-flex>
  </div>

  <GpsPositionListDialog
    v-if="isViewingGpsPositionDialog"
    :isViewingGpsPositionDialog.sync="isViewingGpsPositionDialog"
    :gpsPositionData="gpsPositionData"
    :jobDetails="jobDetails"
    @showSelectedRangeOnMap="showSelectedRangeOnMap"
  ></GpsPositionListDialog>
</div>
